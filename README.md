# 🎵 音乐解锁服务后端系统

基于 **UnblockNeteaseMusic** 的音乐解锁服务后端系统，提供完整的 RESTful API 和独立测试工具。

**版本**: v1.0.0 | **状态**: ✅ 生产就绪 | **更新**: 2025-08-01

---

## 🚀 **快速开始**

### **环境要求**
- Node.js >= 16.0.0
- npm >= 8.0.0

### **安装与启动**
```bash
# 1. 安装依赖
npm install

# 2. 启动服务
npm start

# 3. 开发模式（自动重启）
npm run dev
```

### **访问地址**
- **服务主页**: http://localhost:50091
- **API基础地址**: http://localhost:50091/api
- **API文档**: http://localhost:50091/api/docs
- **测试工具**: 打开 `api-test-tool.html`

---

## 📋 **项目概述**

### **核心功能**
- 🎵 **音乐解锁** - 支持6个主流音源的音乐解锁
- 🔍 **多模式操作** - unlock/status/test/sources/detail 五种模式
- 📊 **音源管理** - 统一管理和监控音源状态
- 🛠️ **独立测试工具** - HTML可视化API测试界面
- 📈 **性能优化** - 并行处理，sources模式5倍性能提升

### **技术架构**
- **运行时**: Node.js 16+
- **Web框架**: Express.js 4.18+
- **核心引擎**: @unblockneteasemusic/server v0.27.10
- **日志系统**: Winston + 文件轮转
- **测试框架**: Jest + Supertest + Playwright
- **代码质量**: ESLint + 92.85%测试覆盖率

### **支持音源**
1. **咪咕音乐** (migu) - 主要音源，高音质
2. **酷我音乐** (kuwo) - 无损音质支持
3. **QQ音乐** (qq) - 流行歌曲丰富
4. **酷狗音乐** (kugou) - 综合音源
5. **JOOX** (joox) - 国际音源
6. **YouTube** (youtube) - 备用音源

---

## 🎯 **API接口总览**

本系统提供 **8个API端点**，分为3个功能组：

### **🏥 系统级API (3个)**
| 端点 | 方法 | 功能描述 |
|------|------|----------|
| `/` | GET | 健康检查 + API文档概览 |
| `/api/` | GET | API基本信息和端点列表 |
| `/api/docs` | GET | 完整API文档和使用示例 |

### **🎵 万能解锁API (1个) ⭐**
| 端点 | 方法 | 功能描述 |
|------|------|----------|
| `/api/unlock` | GET | 万能解锁服务（支持5种操作模式） |

**5种操作模式**:
- **`unlock`** - 解锁获取播放链接（默认模式）
- **`status`** - 检查歌曲可用性状态
- **`detail`** - 获取详细歌曲元数据
- **`sources`** - 分析各音源可用性（🚀并行优化）
- **`test`** - 测试音源连通性

### **📊 音源管理API (4个)**
| 端点 | 方法 | 功能描述 |
|------|------|----------|
| `/api/sources` | GET | 获取所有可用音源列表 |
| `/api/sources/stats` | GET | 获取音源统计信息 |
| `/api/sources/config` | GET | 获取音源配置信息 |
| `/api/sources/:sourceId` | GET | 获取单个音源详细信息 |

---

## ⚙️ **配置说明**

### **核心环境变量 (.env)**
```bash
# 基础服务配置
PORT=50091                   # 服务端口
NODE_ENV=development         # 运行环境
HOST=localhost               # 服务主机

# 音乐服务配置
MUSIC_SOURCES=migu,kuwo,qq,kugou,joox,youtube  # 音源优先级
MIN_BR=0                     # 最低音质要求（0=无限制）
ENABLE_FLAC=true             # 启用无损音质
ENABLE_LOCAL_VIP=true        # 启用本地VIP功能
FOLLOW_SOURCE_ORDER=false    # 音源顺序策略
BLOCK_ADS=true               # 广告拦截

# 安全配置
RATE_LIMIT_MAX_REQUESTS=100  # 频率限制
RATE_LIMIT_WINDOW_MS=900000  # 限制时间窗口（15分钟）
CORS_ORIGIN=*                # 跨域配置

# 日志配置
LOG_LEVEL=info               # 日志级别
LOG_FILE_ENABLED=true        # 启用文件日志
LOG_CONSOLE_ENABLED=true     # 启用控制台日志
```

### **音源配置详情**
| 音源ID | 音源名称 | 特点 | 音质支持 |
|--------|----------|------|----------|
| `migu` | 咪咕音乐 | 主要音源，稳定性好 | 128k-999k |
| `kuwo` | 酷我音乐 | 无损音质丰富 | 128k-FLAC |
| `qq` | QQ音乐 | 流行歌曲全面 | 128k-999k |
| `kugou` | 酷狗音乐 | 综合音源 | 128k-320k |
| `joox` | JOOX | 国际音源 | 128k-320k |
| `youtube` | YouTube | 备用音源 | 128k-320k |

---

## 🛠️ **API使用示例**

### **万能解锁API使用**

#### **1. 基础解锁（unlock模式）**
```bash
# 解锁单首歌曲
curl "http://localhost:50091/api/unlock?songIds=418602084"

# 批量解锁多首歌曲
curl "http://localhost:50091/api/unlock?songIds=418602084,185868,1297742167"

# 指定音源解锁
curl "http://localhost:50091/api/unlock?songIds=418602084&sources=migu,kuwo"

# 指定最低音质
curl "http://localhost:50091/api/unlock?songIds=418602084&minBitrate=320000"
```

#### **2. 状态检查（status模式）**
```bash
# 检查歌曲可用性
curl "http://localhost:50091/api/unlock?songIds=418602084&mode=status"

# 批量状态检查
curl "http://localhost:50091/api/unlock?songIds=418602084,185868&mode=status"
```

#### **3. 音源分析（sources模式）**
```bash
# 分析各音源可用性（并行处理）
curl "http://localhost:50091/api/unlock?songIds=418602084&mode=sources"

# 指定特定音源分析
curl "http://localhost:50091/api/unlock?songIds=418602084&mode=sources&sources=migu,qq,kuwo"
```

#### **4. 详细信息（detail模式）**
```bash
# 获取歌曲详细元数据
curl "http://localhost:50091/api/unlock?songIds=418602084&mode=detail"
```

#### **5. 连通性测试（test模式）**
```bash
# 测试所有音源连通性
curl "http://localhost:50091/api/unlock?mode=test"

# 测试指定音源
curl "http://localhost:50091/api/unlock?mode=test&sources=migu,kuwo&testSongId=418602084"
```

### **音源管理API使用**
```bash
# 获取音源列表
curl "http://localhost:50091/api/sources"

# 获取音源统计
curl "http://localhost:50091/api/sources/stats"

# 获取音源配置
curl "http://localhost:50091/api/sources/config"

# 获取单个音源详情
curl "http://localhost:50091/api/sources/migu"
```

### **标准响应格式**
```json
{
  "状态码": 200,
  "消息": "解锁1首歌曲完成",
  "时间戳": "2025-08-01T08:00:00.000Z",
  "数据": {
    "歌曲ID": 418602084,
    "歌曲名": "海阔天空",
    "艺术家": "Beyond",
    "专辑": "乐与怒",
    "播放链接": "http://music.migu.cn/v3/music/player/audio?...",
    "音源ID": "migu",
    "音源名称": "咪咕音乐",
    "音质": 320000,
    "音质描述": "高品质",
    "文件大小": 8456789,
    "时长": 325
  }
}
```

---

## 📈 **性能与优化**

### **性能指标**
| 操作类型 | 响应时间 | 并发能力 | 优化特性 |
|----------|----------|----------|----------|
| 单首解锁 | < 3秒 | 100+ req/min | 智能音源选择 |
| 批量解锁 | < 10秒 (20首) | 50+ req/min | 并行处理 |
| 音源分析 | < 5秒 | 80+ req/min | 🚀并行优化（5倍提升） |
| 状态检查 | < 2秒 | 150+ req/min | 轻量级检查 |
| 连通性测试 | < 8秒 | 30+ req/min | 全音源并行测试 |

### **优化特性**
- ✅ **并行处理**: sources模式使用Promise.all()并行处理，性能提升5倍
- ✅ **智能缓存**: 音源配置和统计信息智能缓存
- ✅ **参数优化**: 移除冗余参数，减少网络开销
- ✅ **音质策略**: MIN_BR=0移除音质过滤，提高成功率
- ✅ **错误处理**: 完善的错误处理和超时机制
- ✅ **日志优化**: 结构化日志，便于监控和调试

### **系统资源**
- **内存占用**: < 100MB (正常运行)
- **CPU使用**: < 10% (空闲时)
- **磁盘空间**: 日志文件自动轮转，最多保留14天
- **网络带宽**: 根据音乐文件大小动态调整

---

## 🧪 **测试与质量保证**

### **测试覆盖率**
```
📊 代码覆盖率: 92.85% (超过90%目标)
├── Services层: 92.85% (核心业务逻辑)
├── Controllers层: 95%+ (API控制器)
├── Utils层: 90%+ (工具函数)
└── Routes层: 100% (路由定义)
```

### **测试类型与结果**
| 测试类型 | 测试数量 | 通过率 | 覆盖范围 |
|----------|----------|--------|----------|
| 单元测试 | 98个 | 100% | 核心功能模块 |
| 集成测试 | 15个 | 100% | API端点测试 |
| E2E测试 | 12个 | 100% | HTML工具测试 |
| 性能测试 | 5个 | 100% | 并发和响应时间 |

### **代码质量指标**
- ✅ **ESLint检查**: 零错误，零警告
- ✅ **代码规范**: 统一格式化和命名规范
- ✅ **JSDoc文档**: 100%函数注释覆盖
- ✅ **错误处理**: 完善的异常捕获和处理
- ✅ **安全检查**: Helmet安全中间件
- ✅ **依赖管理**: 定期更新和安全扫描

### **测试命令**
```bash
# 运行所有测试
npm test

# 运行覆盖率测试
npm run test:coverage

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行E2E测试
npm run test:e2e

# 代码质量检查
npm run lint

# 自动修复代码格式
npm run lint:fix
```

---

## 📁 **项目结构**

```
📦 music-unlock-server (音乐解锁服务)
├── 📄 README.md                    # 项目主文档 ⭐
├── 📄 current-api-reference.md     # API完整参考手册
├── 📄 api-test-tool.html           # 独立API测试工具
├── 📄 package.json                 # 项目配置和依赖
├── 📄 .env                         # 环境变量配置
├── 📄 Dockerfile                   # Docker容器配置
├── 📄 docker-compose.yml           # Docker编排配置
│
├── 📁 src/                         # 源代码目录
│   ├── 📄 app.js                   # Express应用入口
│   ├── 📁 config/                  # 配置模块
│   │   └── 📄 config.js            # 统一配置管理
│   ├── 📁 controllers/             # 控制器层
│   │   ├── 📄 unlockController.js  # 解锁控制器
│   │   └── 📄 sourceController.js  # 音源控制器
│   ├── 📁 services/                # 服务层
│   │   ├── 📄 unlockService.js     # 解锁服务
│   │   └── 📄 musicService.js      # 音乐服务
│   ├── 📁 routes/                  # 路由层
│   │   ├── 📄 index.js             # 路由主入口
│   │   ├── 📄 unlockRoutes.js      # 解锁路由
│   │   └── 📄 sourceRoutes.js      # 音源路由
│   ├── 📁 middleware/              # 中间件
│   │   ├── 📄 logger.js            # 日志中间件
│   │   ├── 📄 errorHandler.js      # 错误处理
│   │   └── 📄 validator.js         # 参数验证
│   └── 📁 utils/                   # 工具函数
│       ├── 📄 response.js          # 响应格式化
│       └── 📄 constants.js         # 常量定义
│
├── 📁 tests/                       # 测试文件目录
│   ├── 📄 api-unit.test.js         # API单元测试
│   ├── 📄 services.test.js         # 服务层测试
│   ├── 📄 utils.test.js            # 工具函数测试
│   ├── 📄 integration.test.js      # 集成测试
│   └── 📄 e2e-html.test.js         # E2E测试
│
├── 📁 logs/                        # 日志文件目录
│   ├── 📄 combined-YYYY-MM-DD.log  # 综合日志
│   ├── 📄 error-YYYY-MM-DD.log     # 错误日志
│   └── 📄 access-YYYY-MM-DD.log    # 访问日志
│
├── 📁 scripts/                     # 脚本文件目录
│   ├── 📄 build.sh                 # 构建脚本
│   ├── 📄 run.sh                   # 运行脚本
│   └── 📄 test-automation.sh       # 测试自动化脚本
│
├── 📁 backup/                      # 备份文件目录
├── 📁 project_document/            # 项目文档目录
└── 📁 node_modules/                # 依赖包目录
```

### **核心文件说明**
- **`src/app.js`**: Express应用主入口，配置中间件和路由
- **`src/controllers/unlockController.js`**: 万能解锁API的核心控制器
- **`src/services/unlockService.js`**: 音乐解锁的核心业务逻辑
- **`api-test-tool.html`**: 独立的HTML测试工具，无需服务器静态资源
- **`.env`**: 环境配置文件，包含190行详细配置说明

---

## 🔧 **开发与部署指南**

### **本地开发环境搭建**
```bash
# 1. 克隆项目（如果有Git仓库）
git clone <repository-url>
cd music-unlock-server

# 2. 安装依赖
npm install

# 3. 配置环境变量
# 复制并编辑.env文件，根据需要调整配置
cp .env.example .env  # 如果有示例文件
# 或直接编辑现有的.env文件

# 4. 启动开发服务器（自动重启）
npm run dev

# 5. 验证服务启动
curl http://localhost:50091/
```

### **生产环境部署**

#### **方式1: 直接部署**
```bash
# 1. 设置生产环境
export NODE_ENV=production

# 2. 安装生产依赖
npm ci --only=production

# 3. 启动服务
npm start

# 4. 后台运行（可选）
nohup npm start > app.log 2>&1 &
```

#### **方式2: Docker部署**
```bash
# 1. 构建镜像
docker build -t music-unlock-server .

# 2. 运行容器
docker run -d -p 50091:50091 --name music-unlock music-unlock-server

# 3. 使用docker-compose（推荐）
docker-compose up -d
```

#### **方式3: 使用脚本部署**
```bash
# 使用项目提供的脚本
./scripts/build.sh    # 构建项目
./scripts/run.sh      # 运行服务
```

### **开发工具命令**
```bash
# 代码质量
npm run lint          # ESLint检查
npm run lint:fix      # 自动修复格式问题

# 测试相关
npm test              # 运行所有测试
npm run test:unit     # 单元测试
npm run test:integration  # 集成测试
npm run test:e2e      # E2E测试
npm run test:coverage # 覆盖率测试

# 开发调试
npm run dev           # 开发模式（nodemon自动重启）
npm start             # 生产模式启动
```

### **服务监控与维护**
```bash
# 健康检查
curl http://localhost:50091/

# 查看服务状态
./scripts/run.sh status

# 查看日志
./scripts/run.sh logs
tail -f logs/combined-$(date +%Y-%m-%d).log

# 重启服务
./scripts/run.sh restart

# 停止服务
./scripts/run.sh stop
```

---

## 📚 **文档与资源**

### **项目文档**
- 📖 **主文档**: `README.md` - 项目概述和使用指南
- 📋 **API参考**: `current-api-reference.md` - 完整API文档
- 🛠️ **测试工具**: `api-test-tool.html` - 独立HTML测试界面
- 📊 **项目总结**: `project_document/PROJECT-SUMMARY.md` - 项目成果总结

### **在线资源**
- 🌐 **服务主页**: http://localhost:50091 - 健康检查和API概览
- 📖 **API文档**: http://localhost:50091/api/docs - 在线API文档
- 🔍 **API信息**: http://localhost:50091/api - API基本信息

### **技术支持**
- 📝 **日志文件**: `logs/` 目录下的详细日志
- 🧪 **测试报告**: 运行 `npm run test:coverage` 查看
- 🔧 **配置文件**: `.env` 文件包含详细配置说明
- 📊 **性能监控**: Winston日志系统记录详细性能数据

---

## 🎯 **项目状态总览**

### **功能完整性**
- ✅ **8个API端点** - 全部实现并测试通过
- ✅ **5种操作模式** - unlock/status/test/sources/detail
- ✅ **6个音源支持** - migu/kuwo/qq/kugou/joox/youtube
- ✅ **独立测试工具** - HTML界面完全独立运行

### **质量保证**
- ✅ **92.85%测试覆盖率** - 超过90%目标
- ✅ **ESLint零错误** - 代码质量检查通过
- ✅ **98个单元测试** - 全部通过
- ✅ **完整文档** - 100%API文档覆盖

### **性能优化**
- ✅ **并行处理** - sources模式5倍性能提升
- ✅ **智能缓存** - 音源配置和统计缓存
- ✅ **错误处理** - 完善的异常处理机制
- ✅ **日志系统** - 结构化日志和文件轮转

### **生产就绪**
- ✅ **Docker支持** - 容器化部署配置
- ✅ **环境配置** - 详细的.env配置文件
- ✅ **脚本工具** - 构建、运行、测试脚本
- ✅ **监控支持** - 健康检查和状态监控

---

**项目版本**: v1.0.0
**最后更新**: 2025-08-01
**项目状态**: 🚀 **生产就绪**
**技术栈**: Node.js + Express.js + UnblockNeteaseMusic
**开发团队**: Music Unlock Server Team
