/**
 * 单元测试脚本 - 不启动服务器
 * 直接测试Express应用实例
 */

const request = require('supertest');

// 设置测试超时时间
jest.setTimeout(30000);

// 测试配置
const TEST_SONG_ID = '418602084'; // 周杰伦-稻香
const API_BASE = '/api';

describe('音乐解锁服务API单元测试', () => {
    let app;
    
    beforeAll(async () => {
        // 设置测试环境变量
        process.env.NODE_ENV = 'test';
        process.env.PORT = '3001'; // 使用测试专用端口

        // 动态导入app以避免端口冲突
        app = require('../src/app');
    });
    
    // 健康检查测试
    describe('健康检查', () => {
        test('GET /health - 应该返回服务健康状态', async () => {
            const response = await request(app)
                .get('/health')
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.status).toBe('healthy');
        });
    });

    // API信息测试
    describe('API信息', () => {
        test('GET /api - 应该返回API基本信息', async () => {
            const response = await request(app)
                .get(API_BASE)
                .expect(200);

            expect(response.body.name).toBe('音乐解锁服务API');
            expect(response.body.version).toBeDefined();
        });

        test('GET /api/docs - 应该返回API文档', async () => {
            const response = await request(app)
                .get(`${API_BASE}/docs`)
                .expect(200);

            expect(response.body.title).toBe('音乐解锁服务API文档');
            expect(response.body.endpoints).toBeDefined();
        });
    });

    // 万能解锁API测试
    describe('万能解锁API', () => {
        test('GET /api/unlock?songIds=:id&mode=detail - 应该返回歌曲详细信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=detail&format=full`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/unlock?songIds=:id&mode=unlock - 应该返回解锁信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=full`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });

        test('GET /api/unlock?songIds=:id&mode=status - 应该检查歌曲状态', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=status&format=head`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据).toBeDefined();
        });

        test('GET /api/unlock?mode=test&sources=qq - 应该测试音源可用性', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?mode=test&sources=qq`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(Array.isArray(response.body.数据)).toBe(true);
            expect(response.body.数据.length).toBeGreaterThan(0);
            expect(response.body.数据[0]).toHaveProperty('音源ID');
            expect(response.body.数据[0]).toHaveProperty('状态');
        });

        test('GET /api/unlock?songIds=:id&mode=sources - 应该返回音源信息', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=sources&format=full`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据).toBeDefined();
        });
    });

    // 搜索API测试 - 已删除，搜索功能不在当前API范围内

    // 解锁API测试
    describe('解锁API', () => {
        test('GET /api/unlock?songIds=:id&mode=unlock - 批量解锁', async () => {
            const response = await request(app)
                .get(`${API_BASE}/unlock?songIds=${TEST_SONG_ID}&mode=unlock&format=full&minBitrate=128000`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(response.body.数据.歌曲ID).toBe(parseInt(TEST_SONG_ID));
        });
    });

    // 音源管理API测试
    describe('音源管理API', () => {
        test('GET /api/sources - 获取音源列表', async () => {
            const response = await request(app)
                .get(`${API_BASE}/sources`)
                .expect(200);

            expect(response.body.状态码).toBe(200);
            expect(Array.isArray(response.body.数据.音源列表)).toBe(true);
        });
    });

    // 错误处理测试
    describe('错误处理', () => {
        test('GET /api/unlock?songIds=invalid - 无效歌曲ID应返回400', async () => {
            await request(app)
                .get(`${API_BASE}/unlock?songIds=invalid&mode=unlock`)
                .expect(400);
        });

        test('GET /api/nonexistent - 不存在的路径应返回404', async () => {
            await request(app)
                .get(`${API_BASE}/nonexistent`)
                .expect(404);
        });


    });

    // 性能测试
    describe('性能测试', () => {
        test('API响应时间应在合理范围内', async () => {
            const start = Date.now();
            await request(app)
                .get('/health')
                .expect(200);
            const duration = Date.now() - start;
            
            expect(duration).toBeLessThan(1000); // 应在1秒内响应
        });
    });
});
