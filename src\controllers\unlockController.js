/**
 * 解锁控制器
 * 处理音乐解锁相关的API请求
 */

const { unlockSong, unlockSongsBatch, testSourceAvailability } = require('../services/unlockService');
const { isValidSongId, getSongInfo } = require('../services/musicService');
const { success, badRequest, notFound } = require('../utils/response');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness } = require('../middleware/logger');
const { API_LIMITS } = require('../utils/constants');

/**
 * 批量解锁歌曲
 * GET /api/unlock
 */
const unlockSongs = asyncHandler(async (req, res) => {
    let { songIds, sources, minBitrate = 128000, detailed = false } = req.query;

    // 解析GET请求中的数组参数
    if (typeof songIds === 'string') {
        songIds = songIds.split(',').map(id => parseInt(id.trim()));
    }
    if (typeof sources === 'string') {
        sources = sources.split(',').map(s => s.trim());
    }

    // 转换数据类型
    minBitrate = parseInt(minBitrate) || 128000;
    detailed = detailed === 'true';

    logBusiness('批量解锁请求', { count: songIds?.length, sources, minBitrate, detailed });

    // 验证歌曲ID列表
    if (!Array.isArray(songIds) || songIds.length === 0) {
        return badRequest(res, '歌曲ID列表不能为空');
    }

    if (songIds.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量解锁数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个歌曲ID
    const invalidIds = songIds.filter(id => !isValidSongId(id));
    if (invalidIds.length > 0) {
        return badRequest(res, `无效的歌曲ID: ${invalidIds.join(', ')}`);
    }

    // 检查重复ID
    const uniqueIds = [...new Set(songIds.map(id => parseInt(id)))];
    if (uniqueIds.length !== songIds.length) {
        return badRequest(res, '歌曲ID列表包含重复项');
    }

    // 音质验证已简化 - 接受任何有效数值
    minBitrate = Math.max(0, minBitrate); // 确保非负数

    const result = await unlockSongsBatch(uniqueIds, sources, {
        minBitrate,
        detailed
    });

    // 转换为中文字段并添加统计信息
    const response = {
        成功列表: result.success,
        失败列表: result.failed,
        总数: result.total,
        成功数量: result.successCount,
        失败数量: result.failedCount,
        统计信息: {
            总数: result.total,
            成功数量: result.successCount,
            失败数量: result.failedCount,
            成功率: ((result.successCount / result.total) * 100).toFixed(2) + '%'
        }
    };

    success(res, response, `批量解锁完成，成功: ${result.successCount}/${result.total}`);
});

/**
 * 单首歌曲解锁
 * GET /api/unlock/:id
 */
const unlockSingleSong = asyncHandler(async (req, res) => {
    const { id } = req.params;
    let { sources, minBitrate = 128000 } = req.query;

    // 解析GET请求中的参数
    if (typeof sources === 'string') {
        sources = sources.split(',').map(s => s.trim());
    }
    minBitrate = parseInt(minBitrate) || 128000;

    logBusiness('单首解锁请求', { songId: id, sources, minBitrate });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    // 音质验证已简化 - 接受任何有效数值
    minBitrate = Math.max(0, minBitrate); // 确保非负数

    try {
        const result = await unlockSong(id, sources);

        success(res, result, '歌曲解锁成功');

    } catch (error) {
        if (error.statusCode === 404) {
            return notFound(res, `歌曲 ${id} 无法解锁`);
        }
    
        throw error;
    }
});

/**
 * 快速解锁（仅返回播放URL）
 * GET /api/unlock/quick
 */
const quickUnlock = asyncHandler(async (req, res) => {
    let { songIds, sources } = req.query;

    // 解析GET请求中的数组参数
    if (typeof songIds === 'string') {
        songIds = songIds.split(',').map(id => parseInt(id.trim()));
    }
    if (typeof sources === 'string') {
        sources = sources.split(',').map(s => s.trim());
    }

    logBusiness('快速解锁请求', { songIds, count: songIds?.length, sources });

    // 验证歌曲ID列表
    if (!Array.isArray(songIds) || songIds.length === 0) {
        return badRequest(res, '歌曲ID列表不能为空');
    }

    if (songIds.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量解锁数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个歌曲ID
    const invalidIds = songIds.filter(id => !isValidSongId(id));
    if (invalidIds.length > 0) {
        return badRequest(res, `无效的歌曲ID: ${invalidIds.join(', ')}`);
    }

    const result = await unlockSongsBatch(songIds, sources, {
        minBitrate: 128000,
        detailed: false
    });

    // 只返回成功的结果，简化格式
    const quickResults = result.success.map(item => ({
        歌曲ID: item.歌曲ID,
        播放链接: item.播放链接,
        音源ID: item.音源ID
    }));

    const response = {
        解锁结果: quickResults,
        总数: result.total,
        成功数量: result.successCount,
        失败ID列表: result.failed.map(item => item.歌曲ID)
    };

    success(res, response, `快速解锁完成，成功: ${result.successCount}/${result.total}`);
});

/**
 * 检查解锁状态
 * GET /api/unlock/status/:id
 */
const checkUnlockStatus = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { sources } = req.query;

    logBusiness('检查解锁状态', { songId: id, sources });

    // 验证歌曲ID
    if (!isValidSongId(id)) {
        return badRequest(res, '无效的歌曲ID格式');
    }

    try {
        const sourcesArray = sources ? sources.split(',').map(s => s.trim()) : null;
    
        // 尝试解锁来检查状态
        const result = await unlockSong(id, sourcesArray);

        const status = {
            歌曲ID: parseInt(id),
            可用状态: true,
            音源ID: result.音源ID,
            音源名称: result.音源名称,
            音质: result.音质,
            音质描述: getQualityDescription(result.音质),
            检查时间: new Date().toISOString()
        };

        success(res, status, '解锁状态检查完成');

    } catch (error) {
        if (error.statusCode === 404) {
            const status = {
                歌曲ID: parseInt(id),
                可用状态: false,
                失败原因: '无可用音源',
                检查时间: new Date().toISOString()
            };
      
            success(res, status, '歌曲暂时无法解锁');
        } else {
            throw error;
        }
    }
});

/**
 * 批量检查解锁状态
 * GET /api/unlock/status/batch
 */
const checkBatchUnlockStatus = asyncHandler(async (req, res) => {
    let { songIds, sources } = req.query;

    // 解析GET请求中的数组参数
    if (typeof songIds === 'string') {
        songIds = songIds.split(',').map(id => parseInt(id.trim()));
    }
    if (typeof sources === 'string') {
        sources = sources.split(',').map(s => s.trim());
    }

    logBusiness('批量检查解锁状态', { count: songIds?.length, sources });

    // 验证歌曲ID列表
    if (!Array.isArray(songIds) || songIds.length === 0) {
        return badRequest(res, '歌曲ID列表不能为空');
    }

    if (songIds.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量检查数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个歌曲ID
    const invalidIds = songIds.filter(id => !isValidSongId(id));
    if (invalidIds.length > 0) {
        return badRequest(res, `无效的歌曲ID: ${invalidIds.join(', ')}`);
    }

    const results = [];
    const concurrency = 3; // 限制并发数

    // 分批处理
    for (let i = 0; i < songIds.length; i += concurrency) {
        const batch = songIds.slice(i, i + concurrency);

        const batchPromises = batch.map(async (songId) => {
            try {
                const result = await unlockSong(songId, sources);
                return {
                    歌曲ID: parseInt(songId),
                    可用状态: true,
                    音源ID: result.音源ID,
                    音源名称: result.音源名称,
                    音质: result.音质,
                    音质描述: getQualityDescription(result.音质)
                };
            } catch (error) {
                return {
                    歌曲ID: parseInt(songId),
                    可用状态: false,
                    失败原因: error.message
                };
            }
        });

        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
    }

    const summary = {
        总数: results.length,
        可用: results.filter(r => r.可用状态).length,
        不可用: results.filter(r => !r.可用状态).length,
        检查时间: new Date().toISOString()
    };

    const response = {
        检查结果: results,
        统计信息: summary
    };

    success(res, response, `批量状态检查完成，可用: ${summary.可用}/${summary.总数}`);
});

/**
 * 根据比特率获取音质描述
 * @param {number} bitrate - 比特率
 * @returns {string} 音质描述
 */
function getQualityDescription(bitrate) {
    if (bitrate >= 999000) return '无损';
    if (bitrate >= 320000) return '极高';
    if (bitrate >= 192000) return '较高';
    if (bitrate >= 128000) return '标准';
    return '低质量';
}

/**
 * 万能解锁API - 统一处理所有解锁相关功能
 * GET /api/unlock
 *
 * 查询参数：
 * - songIds: 歌曲ID，支持单个或逗号分隔的多个ID
 * - mode: 操作模式 (unlock|status|test|sources|detail)
 * - format: 返回格式 (full|minimal|head)
 * - detailed: 是否返回详细信息 (true|false)
 * - sources: 指定音源列表，逗号分隔
 * - includeSources: 是否包含音源信息 (true|false)
 * - includeMetadata: 是否包含元数据 (true|false)
 * - minBitrate: 最低音质要求
 * - testSongId: 测试模式下使用的歌曲ID
 */
const superUnlock = asyncHandler(async (req, res) => {
    const {
        songIds,
        mode = 'unlock',
        format = 'full',
        detailed = 'true',
        sources,
        minBitrate = '128000',
        testSongId = '418602084'
    } = req.query;

    // 记录业务日志
    logBusiness('万能解锁API请求', {
        songIds, mode, format, detailed, sources, minBitrate
    });

    // 参数验证
    const validModes = ['unlock', 'status', 'test', 'sources', 'detail'];
    if (!validModes.includes(mode)) {
        return badRequest(res, `无效的操作模式，支持: ${validModes.join(', ')}`);
    }

    const validFormats = ['full', 'minimal', 'head'];
    if (!validFormats.includes(format)) {
        return badRequest(res, `无效的返回格式，支持: ${validFormats.join(', ')}`);
    }

    // 转换布尔参数
    const isDetailed = detailed === 'true';
    const minBitrateNum = parseInt(minBitrate) || 128000;

    // 解析音源列表
    let sourcesArray = null;
    if (sources) {
        sourcesArray = sources.split(',').map(s => s.trim()).filter(s => s);
        // 验证音源有效性
        const validSources = ['qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'ytdlp', 'bilibili'];
        const invalidSources = sourcesArray.filter(source => !validSources.includes(source));
        if (invalidSources.length > 0) {
            return badRequest(res, `无效的音源: ${invalidSources.join(', ')}`);
        }
    }

    try {
        // 根据mode参数分支处理
        switch (mode) {
        case 'test':
            return await handleTestMode(res, sourcesArray, testSongId, format);

        case 'unlock':
        case 'status':
        case 'sources':
        case 'detail':
            return await handleSongModes(res, {
                songIds, mode, format, isDetailed, sourcesArray, minBitrateNum
            });

        default:
            return badRequest(res, '不支持的操作模式');
        }
    } catch (error) {
        logBusiness('万能解锁API错误', { error: error.message, songIds, mode });
        throw error;
    }
});

/**
 * 处理音源测试模式
 */
async function handleTestMode(res, sourcesArray, testSongId, format) {
    if (!sourcesArray || sourcesArray.length === 0) {
        return badRequest(res, '测试模式需要指定音源列表');
    }

    const testId = parseInt(testSongId) || 418602084;
    if (!isValidSongId(testId)) {
        return badRequest(res, '无效的测试歌曲ID');
    }

    const results = [];
    for (const source of sourcesArray) {
        try {
            const isAvailable = await testSourceAvailability(source);
            results.push({
                音源ID: source,
                状态: isAvailable ? '可用' : '不可用',
                测试歌曲ID: testId,
                测试时间: new Date().toISOString()
            });
        } catch (error) {
            // 确保错误信息能正确显示
            let errorMessage = '未知错误';
            if (error && typeof error === 'object') {
                errorMessage = error.message || error.toString() || '测试异常';
            } else if (typeof error === 'string') {
                errorMessage = error;
            }

            results.push({
                音源ID: source,
                状态: '测试失败',
                错误信息: errorMessage,
                测试歌曲ID: testId,
                测试时间: new Date().toISOString()
            });
        }
    }

    // 根据format参数返回不同格式
    let responseData;
    switch (format) {
    case 'head':
        responseData = {
            总数: results.length,
            可用数: results.filter(r => r.状态 === '可用').length
        };
        break;
    case 'minimal':
        responseData = results.map(r => ({
            音源ID: r.音源ID,
            状态: r.状态
        }));
        break;
    case 'full':
    default:
        responseData = results;
        break;
    }

    success(res, responseData, '音源测试完成');
}

/**
 * 处理歌曲相关模式
 */
async function handleSongModes(res, options) {
    const {
        songIds, mode, format, isDetailed, sourcesArray, minBitrateNum
    } = options;

    // 解析歌曲ID列表
    let idsArray;
    if (typeof songIds === 'string') {
        idsArray = songIds.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
    } else if (Array.isArray(songIds)) {
        idsArray = songIds.map(id => parseInt(id)).filter(id => !isNaN(id));
    } else {
        return badRequest(res, '歌曲ID参数格式错误');
    }

    if (idsArray.length === 0) {
        return badRequest(res, '歌曲ID列表不能为空');
    }

    if (idsArray.length > API_LIMITS.MAX_BATCH_SIZE) {
        return badRequest(res, `批量处理数量超限，最大: ${API_LIMITS.MAX_BATCH_SIZE}`);
    }

    // 验证每个歌曲ID
    const invalidIds = idsArray.filter(id => !isValidSongId(id));
    if (invalidIds.length > 0) {
        return badRequest(res, `无效的歌曲ID: ${invalidIds.join(', ')}`);
    }

    // 去重
    const uniqueIds = [...new Set(idsArray)];

    try {
        let results;

        // 根据mode处理
        switch (mode) {
        case 'detail':
            results = await handleDetailMode(uniqueIds, sourcesArray, format, isDetailed);
            break;
        case 'unlock':
            results = await handleUnlockMode(uniqueIds, sourcesArray, format, isDetailed, minBitrateNum);
            break;
        case 'status':
            results = await handleStatusMode(uniqueIds, sourcesArray, format);
            break;
        case 'sources':
            results = await handleSourcesMode(uniqueIds, sourcesArray, format);
            break;
        default:
            return badRequest(res, '不支持的歌曲操作模式');
        }

        // 构建响应消息
        const message = getModeMessage(mode, uniqueIds.length);
        success(res, results, message);

    } catch (error) {
        logBusiness('歌曲模式处理错误', { error: error.message, mode, songIds: uniqueIds });
        throw error;
    }
}

/**
 * 处理详情模式
 */
async function handleDetailMode(songIds, sources, format, detailed) {
    if (songIds.length === 1) {
        const songInfo = await getSongInfo(songIds[0], sources);
        return formatSingleResult(songInfo, format, detailed);
    } else {
        const results = [];
        for (const songId of songIds) {
            try {
                const songInfo = await getSongInfo(songId, sources);
                results.push(formatSingleResult(songInfo, format, detailed));
            } catch (error) {
                results.push({
                    歌曲ID: songId,
                    状态: '获取失败',
                    错误信息: error.message
                });
            }
        }
        return results;
    }
}

/**
 * 处理解锁模式
 */
async function handleUnlockMode(songIds, sources, format, detailed, minBitrate) {
    if (songIds.length === 1) {
        const result = await unlockSong(songIds[0], sources, { minBitrate });
        return formatUnlockResult(result, format, detailed);
    } else {
        const result = await unlockSongsBatch(songIds, sources, {
            minBitrate,
            detailed
        });
        return formatBatchResult(result, format);
    }
}

/**
 * 处理状态模式
 */
async function handleStatusMode(songIds, sources, format) {
    const results = [];
    for (const songId of songIds) {
        try {
            const result = await unlockSong(songId, sources);
            results.push({
                歌曲ID: songId,
                状态: result.播放链接 ? '可用' : '不可用',
                音源: result.音源ID || '未知',
                检查时间: new Date().toISOString()
            });
        } catch (error) {
            results.push({
                歌曲ID: songId,
                状态: '检查失败',
                错误信息: error.message,
                检查时间: new Date().toISOString()
            });
        }
    }

    if (format === 'head') {
        return {
            总数: results.length,
            可用数: results.filter(r => r.状态 === '可用').length
        };
    }

    return results;
}

/**
 * 处理音源模式 - 优化版本：并行处理音源测试
 */
async function handleSourcesMode(songIds, sources, format) {
    // 并行处理每首歌曲
    const songPromises = songIds.map(async (songId) => {
        try {
            // 循环测试所有音源或指定音源
            const testSources = sources || ['qq', 'kugou', 'kuwo', 'migu', 'joox'];

            // 并行测试所有音源 - 性能优化关键点
            const sourcePromises = testSources.map(async (source) => {
                try {
                    const result = await unlockSong(songId, [source]);
                    if (result.播放链接) {
                        return {
                            音源ID: source,
                            音质: result.音质 || '未知',
                            比特率: result.比特率 || 0
                        };
                    }
                    return null;
                } catch {
                    // 忽略单个音源的错误
                    return null;
                }
            });

            // 等待所有音源测试完成
            const sourceResults = await Promise.all(sourcePromises);
            const availableSources = sourceResults.filter(result => result !== null);

            return {
                歌曲ID: songId,
                可用音源: availableSources,
                音源数量: availableSources.length
            };
        } catch (error) {
            return {
                歌曲ID: songId,
                可用音源: [],
                音源数量: 0,
                错误信息: error.message
            };
        }
    });

    // 等待所有歌曲处理完成
    const results = await Promise.all(songPromises);

    if (format === 'head') {
        return {
            总数: results.length,
            有音源数: results.filter(r => r.音源数量 > 0).length
        };
    }

    return results;
}

/**
 * 格式化单个结果
 */
function formatSingleResult(data, format, detailed) {
    switch (format) {
    case 'head':
        return {
            歌曲ID: data.歌曲ID,
            状态: data.播放链接 ? '可用' : '不可用'
        };
    case 'minimal':
        return {
            歌曲ID: data.歌曲ID,
            歌曲名: data.歌曲名,
            艺术家: data.艺术家,
            播放链接: data.播放链接
        };
    case 'full':
    default:
        return detailed ? data : {
            歌曲ID: data.歌曲ID,
            歌曲名: data.歌曲名,
            艺术家: data.艺术家,
            播放链接: data.播放链接,
            音源ID: data.音源ID,
            音质: data.音质
        };
    }
}

/**
 * 格式化解锁结果
 */
function formatUnlockResult(data, format, detailed) {
    return formatSingleResult(data, format, detailed);
}

/**
 * 格式化批量结果
 */
function formatBatchResult(data, format) {
    if (format === 'head') {
        return {
            总数: data.总数,
            成功数: data.成功数,
            失败数: data.失败数
        };
    }
    return data;
}

/**
 * 获取模式对应的消息
 */
function getModeMessage(mode, count) {
    const messages = {
        detail: `获取${count}首歌曲详情完成`,
        unlock: `解锁${count}首歌曲完成`,
        status: `检查${count}首歌曲状态完成`,
        sources: `获取${count}首歌曲音源信息完成`
    };
    return messages[mode] || '操作完成';
}

module.exports = {
    unlockSongs,
    unlockSingleSong,
    quickUnlock,
    checkUnlockStatus,
    checkBatchUnlockStatus,
    superUnlock
};
