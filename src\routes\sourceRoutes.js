/**
 * 音源管理相关路由
 */

const express = require('express');
const router = express.Router();

// 导入控制器
const {
    getSources,
    getSourceDetail,
    getSourceStats,
    getSourceConfig
} = require('../controllers/sourceController');

// 导入验证中间件
const { validators } = require('../middleware/validator');

// 获取音源统计信息 - 必须放在/:sourceId之前
router.get('/stats', getSourceStats);

// 获取音源配置信息 - 必须放在/:sourceId之前
router.get('/config', getSourceConfig);

// 获取所有可用音源列表
router.get('/', validators.validateGetSources, getSources);

// 获取单个音源详细信息
router.get('/:sourceId', getSourceDetail);

module.exports = router;
