/**
 * 音乐解锁服务模块
 * 基于UnblockNeteaseMusic实现的音乐解锁核心服务
 */

const match = require('@unblockneteasemusic/server');
const config = require('../config/config');
const { logError, logPerformance, logBusiness } = require('../middleware/logger');
const { ServiceUnavailableError, NotFoundError } = require('../middleware/errorHandler');
const { MUSIC_SOURCES, SOURCE_DISPLAY_NAMES } = require('../utils/constants');

/**
 * 设置UnblockNeteaseMusic全局配置
 */
function setupGlobalConfig() {
    // 设置代理（如果配置了）
    if (process.env.PROXY_URL) {
        global.proxy = require('url').parse(process.env.PROXY_URL);
    }

    // 设置hosts（如果配置了）
    if (process.env.CUSTOM_HOSTS) {
        try {
            global.hosts = JSON.parse(process.env.CUSTOM_HOSTS);
        } catch (error) {
            logError(error, { context: 'setup_global_hosts' });
        }
    }

    // 设置音源相关的环境变量
    if (config.music.neteaseCookie) {
        process.env.NETEASE_COOKIE = config.music.neteaseCookie;
    }
    if (config.music.qqCookie) {
        process.env.QQ_COOKIE = config.music.qqCookie;
    }
    if (config.music.miguCookie) {
        process.env.MIGU_COOKIE = config.music.miguCookie;
    }
    if (config.music.jooxCookie) {
        process.env.JOOX_COOKIE = config.music.jooxCookie;
    }
    if (config.music.youtubeKey) {
        process.env.YOUTUBE_KEY = config.music.youtubeKey;
    }

    // 设置功能开关
    process.env.ENABLE_FLAC = config.music.enableFlac.toString();
    process.env.ENABLE_LOCAL_VIP = config.music.enableLocalVip.toString();
    // MIN_BR已移除 - UnblockNeteaseMusic将使用默认音质处理
    process.env.FOLLOW_SOURCE_ORDER = config.music.followSourceOrder.toString();
    process.env.BLOCK_ADS = config.music.blockAds.toString();
}

// 初始化全局配置
setupGlobalConfig();

/**
 * 解锁单首歌曲
 * @param {number|string} songId - 网易云音乐歌曲ID
 * @param {Array<string>} sources - 指定的音源列表，默认使用配置中的音源
 * @returns {Promise<Object>} 解锁结果
 */
async function unlockSong(songId, sources = null) {
    const startTime = Date.now();
    const useSources = sources || config.music.sources;

    try {
        logBusiness('开始解锁歌曲', { songId, sources: useSources });

        // 验证歌曲ID
        const numericSongId = parseInt(songId);
        if (isNaN(numericSongId) || numericSongId <= 0) {
            throw new Error(`无效的歌曲ID: ${songId}`);
        }

        // 调用UnblockNeteaseMusic的match函数 (使用配置化超时)
        const result = await Promise.race([
            match(numericSongId, useSources),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('解锁请求超时')), config.timeout.unlock)
            )
        ]);

        const duration = Date.now() - startTime;
        logPerformance('歌曲解锁', duration, { songId, success: !!result });

        if (!result) {
            throw new NotFoundError(`歌曲 ${songId} 未找到可用音源`);
        }

        // 格式化返回结果
        const formattedResult = formatUnlockResult(result, songId);
    
        logBusiness('歌曲解锁成功', {
            songId,
            source: formattedResult.音源ID,
            bitrate: formattedResult.音质
        });

        return formattedResult;

    } catch (error) {
        const duration = Date.now() - startTime;
        logPerformance('歌曲解锁失败', duration, { songId, error: error.message });

        // 处理不同类型的错误
        if (error instanceof NotFoundError) {
            throw error;
        }

        if (error.message === '解锁请求超时') {
            logError(error, { context: 'unlock_song_timeout', songId, sources: useSources, duration });
            throw new ServiceUnavailableError(`歌曲 ${songId} 解锁超时，请稍后重试`);
        }

        if (error.message && error.message.includes('网络')) {
            logError(error, { context: 'unlock_song_network', songId, sources: useSources });
            throw new ServiceUnavailableError(`网络连接异常，无法解锁歌曲 ${songId}`);
        }

        logError(error, { context: 'unlock_song', songId, sources: useSources });
        throw new ServiceUnavailableError(`解锁歌曲 ${songId} 失败: ${error.message}`);
    }
}

/**
 * 批量解锁歌曲
 * @param {Array<number|string>} songIds - 歌曲ID列表
 * @param {Array<string>} sources - 指定的音源列表
 * @param {Object} options - 解锁选项
 * @returns {Promise<Object>} 批量解锁结果
 */
async function unlockSongsBatch(songIds, sources = null, options = {}) {
    const startTime = Date.now();
    const { detailed = false } = options;

    logBusiness('开始批量解锁', { count: songIds.length, sources, options });

    const results = {
        success: [],
        failed: [],
        total: songIds.length,
        successCount: 0,
        failedCount: 0
    };

    // 使用配置化的并发数量避免过载
    const concurrency = config.performance.batchConcurrency;
    const chunks = [];
    for (let i = 0; i < songIds.length; i += concurrency) {
        chunks.push(songIds.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
        const promises = chunk.map(async (songId) => {
            try {
                const result = await unlockSong(songId, sources);

                const successResult = detailed ? result : {
                    歌曲ID: result.歌曲ID,
                    播放链接: result.播放链接,
                    音源ID: result.音源ID
                };

                results.success.push(successResult);
                results.successCount++;
        
            } catch (error) {
                results.failed.push({
                    歌曲ID: parseInt(songId),
                    错误信息: error.message,
                    错误代码: error.errorCode || 'UNLOCK_FAILED'
                });
                results.failedCount++;
            }
        });

        await Promise.all(promises);
    }

    const duration = Date.now() - startTime;
    logPerformance('批量解锁完成', duration, {
        total: results.total,
        success: results.successCount,
        failed: results.failedCount
    });

    logBusiness('批量解锁完成', {
        total: results.total,
        successCount: results.successCount,
        failedCount: results.failedCount
    });

    return results;
}

/**
 * 格式化解锁结果
 * @param {Object} rawResult - UnblockNeteaseMusic返回的原始结果
 * @param {number|string} songId - 歌曲ID
 * @returns {Object} 格式化后的结果
 */
function formatUnlockResult(rawResult, songId) {
    return {
        歌曲ID: parseInt(songId),
        播放链接: rawResult.url,
        音源ID: rawResult.source || 'unknown',
        音源名称: SOURCE_DISPLAY_NAMES[rawResult.source] || rawResult.source,
        音质: rawResult.br || rawResult.bitrate || 0,
        文件大小: rawResult.size || 0,
        格式: rawResult.type || getFileTypeFromUrl(rawResult.url),
        解锁时间: new Date().toISOString()
    };
}

/**
 * 从URL推断文件类型
 * @param {string} url - 音乐文件URL
 * @returns {string} 文件类型
 */
function getFileTypeFromUrl(url) {
    if (!url) return 'unknown';
  
    const extension = url.split('.').pop().toLowerCase();
    const typeMap = {
        'mp3': 'mp3',
        'flac': 'flac',
        'm4a': 'm4a',
        'aac': 'aac',
        'ogg': 'ogg',
        'wav': 'wav'
    };
  
    return typeMap[extension] || 'mp3';
}

/**
 * 获取可用音源列表及其状态
 * @returns {Promise<Array>} 音源列表
 */
async function getAvailableSources() {
    const sources = Object.values(MUSIC_SOURCES).map(source => ({
        id: source,
        name: SOURCE_DISPLAY_NAMES[source] || source,
        enabled: config.music.sources.includes(source),
        priority: config.music.sources.indexOf(source) + 1 || 999
    }));

    // 按优先级排序
    sources.sort((a, b) => a.priority - b.priority);

    return sources;
}

/**
 * 测试音源可用性
 * @param {string} source - 音源ID
 * @returns {Promise<boolean>} 是否可用
 */
async function testSourceAvailability(source) {
    try {
        // 使用一个已知的测试歌曲ID进行测试
        const testSongId = 418602084; // 周杰伦 - 稻香

        // 使用配置化的音源测试超时时间
        const result = await Promise.race([
            match(testSongId, [source]),
            new Promise((_, reject) =>
                setTimeout(() => reject(new Error('音源测试超时')), config.timeout.sourceTest)
            )
        ]);

        // 检查结果是否有效
        if (result && (result.url || result.播放链接)) {
            logBusiness('音源测试成功', { source, testSongId });
            return true;
        } else {
            logBusiness('音源测试失败 - 无有效结果', { source, testSongId, result: !!result });
            return false;
        }
    } catch (error) {
        // 详细记录错误信息
        const errorInfo = {
            context: 'test_source',
            source,
            errorMessage: error.message,
            errorName: error.name,
            errorCode: error.code
        };

        logError(error, errorInfo);
        logBusiness('音源测试异常', errorInfo);
        return false;
    }
}

module.exports = {
    unlockSong,
    unlockSongsBatch,
    getAvailableSources,
    testSourceAvailability,
    formatUnlockResult
};
