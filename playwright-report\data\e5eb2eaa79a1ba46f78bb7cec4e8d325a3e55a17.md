# Page snapshot

```yaml
- banner:
  - heading "🎵 音乐解锁服务" [level=1]
  - paragraph: 基于UnblockNeteaseMusic的音乐解锁服务后端API测试页面
  - text: 🟢 服务正常 v1.0.0
- navigation:
  - button "歌曲信息"
  - button "搜索功能"
  - button "解锁服务"
  - button "音源管理"
  - button "API文档"
- heading "🔓 音乐解锁" [level=2]
- text: "歌曲ID列表:"
- textbox "歌曲ID列表:": "418602084"
- text: "支持多个歌曲ID，每行一个或用逗号分隔 指定音源 (可选):"
- textbox "指定音源 (可选):"
- text: "最低音质要求:"
- combobox "最低音质要求:":
  - option "128kbps (标准)"
  - option "192kbps (较高)"
  - option "320kbps (极高)" [selected]
  - option "无损"
- checkbox "返回详细信息"
- text: 返回详细信息
- button "批量解锁"
- button "快速解锁"
- button "检查状态"
- contentinfo:
  - paragraph:
    - text: © 2024 音乐解锁服务 | 基于
    - link "UnblockNeteaseMusic":
      - /url: https://github.com/UnblockNeteaseMusic/server
  - paragraph: 仅供学习和研究使用，请支持正版音乐
```