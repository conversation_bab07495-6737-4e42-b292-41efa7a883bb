/**
 * P1阶段配置测试脚本
 * 测试API限制和缓存配置是否正确加载
 */

console.log('🔍 P1阶段配置测试开始...\n');

try {
    // 测试配置文件加载
    console.log('1. 测试配置文件加载...');
    const config = require('./src/config/config');
    
    // 检查limits配置
    console.log('2. 检查API限制配置...');
    if (config.limits) {
        console.log('✅ limits配置存在');
        console.log(`   - maxBatchSize: ${config.limits.maxBatchSize}`);
        console.log(`   - maxSearchResults: ${config.limits.maxSearchResults}`);
        console.log(`   - maxKeywordLength: ${config.limits.maxKeywordLength}`);
        console.log(`   - requestTimeout: ${config.limits.requestTimeout}`);
    } else {
        console.log('❌ limits配置缺失');
    }
    
    // 检查cache配置
    console.log('\n3. 检查缓存配置...');
    if (config.cache) {
        console.log('✅ cache配置存在');
        console.log(`   - metadataTTL: ${config.cache.metadataTTL}`);
        console.log(`   - searchTTL: ${config.cache.searchTTL}`);
        console.log(`   - unlockTTL: ${config.cache.unlockTTL}`);
        console.log(`   - enabled: ${config.cache.enabled}`);
    } else {
        console.log('❌ cache配置缺失');
    }
    
    // 测试constants.js
    console.log('\n4. 测试constants.js配置读取...');
    const { API_LIMITS, CACHE_CONFIG } = require('./src/utils/constants');
    
    console.log('✅ API_LIMITS配置:');
    console.log(`   - MAX_BATCH_SIZE: ${API_LIMITS.MAX_BATCH_SIZE}`);
    console.log(`   - MAX_SEARCH_RESULTS: ${API_LIMITS.MAX_SEARCH_RESULTS}`);
    console.log(`   - REQUEST_TIMEOUT: ${API_LIMITS.REQUEST_TIMEOUT}`);
    console.log(`   - MAX_KEYWORD_LENGTH: ${API_LIMITS.MAX_KEYWORD_LENGTH}`);
    
    console.log('\n✅ CACHE_CONFIG配置:');
    console.log(`   - METADATA_TTL: ${CACHE_CONFIG.METADATA_TTL}`);
    console.log(`   - SEARCH_TTL: ${CACHE_CONFIG.SEARCH_TTL}`);
    console.log(`   - UNLOCK_TTL: ${CACHE_CONFIG.UNLOCK_TTL}`);
    console.log(`   - ENABLED: ${CACHE_CONFIG.ENABLED}`);
    
    // 验证配置一致性
    console.log('\n5. 验证配置一致性...');
    const limitsMatch = (
        API_LIMITS.MAX_BATCH_SIZE === config.limits.maxBatchSize &&
        API_LIMITS.MAX_SEARCH_RESULTS === config.limits.maxSearchResults &&
        API_LIMITS.REQUEST_TIMEOUT === config.limits.requestTimeout &&
        API_LIMITS.MAX_KEYWORD_LENGTH === config.limits.maxKeywordLength
    );
    
    const cacheMatch = (
        CACHE_CONFIG.METADATA_TTL === config.cache.metadataTTL &&
        CACHE_CONFIG.SEARCH_TTL === config.cache.searchTTL &&
        CACHE_CONFIG.UNLOCK_TTL === config.cache.unlockTTL &&
        CACHE_CONFIG.ENABLED === config.cache.enabled
    );
    
    if (limitsMatch) {
        console.log('✅ API限制配置一致性检查通过');
    } else {
        console.log('❌ API限制配置一致性检查失败');
    }
    
    if (cacheMatch) {
        console.log('✅ 缓存配置一致性检查通过');
    } else {
        console.log('❌ 缓存配置一致性检查失败');
    }
    
    // 测试环境变量
    console.log('\n6. 检查环境变量...');
    const envVars = [
        'MAX_BATCH_SIZE',
        'MAX_SEARCH_RESULTS', 
        'MAX_KEYWORD_LENGTH',
        'REQUEST_TIMEOUT',
        'CACHE_METADATA_TTL',
        'CACHE_SEARCH_TTL',
        'CACHE_UNLOCK_TTL',
        'CACHE_ENABLED'
    ];
    
    envVars.forEach(varName => {
        const value = process.env[varName];
        if (value !== undefined) {
            console.log(`✅ ${varName}: ${value}`);
        } else {
            console.log(`⚠️  ${varName}: 未设置 (使用默认值)`);
        }
    });
    
    console.log('\n🎉 P1阶段配置测试完成！');
    
    if (limitsMatch && cacheMatch) {
        console.log('✅ 所有配置测试通过，P1阶段硬编码优化成功！');
        process.exit(0);
    } else {
        console.log('❌ 配置测试失败，需要检查配置一致性');
        process.exit(1);
    }
    
} catch (error) {
    console.error('❌ 配置测试失败:', error.message);
    console.error(error.stack);
    process.exit(1);
}
