{"name": "music-unlock-server", "version": "1.0.0", "description": "基于UnblockNeteaseMusic的音乐解锁服务后端系统", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:api": "jest tests/api-unit.test.js", "test:integration": "node tests/integration-fixed.test.js", "test:performance": "node tests/performance.test.js", "test:all": "npm run test:unit && npm run test:integration && npm run test:performance", "test:unit": "jest tests/api-unit.test.js tests/utils.test.js tests/services.test.js tests/module-init.test.js tests/coverage-boost.test.js", "test:coverage": "jest tests/api-unit.test.js tests/utils.test.js tests/services.test.js tests/module-init.test.js tests/coverage-boost.test.js --coverage", "test:e2e": "playwright test", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "build": "echo '项目无需编译，直接运行即可'", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["netease", "music", "unlock", "api", "nodejs", "express"], "author": "Music Unlock Server Team", "license": "MIT", "dependencies": {"@unblockneteasemusic/server": "^0.27.10", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"@playwright/test": "^1.54.1", "axios": "^1.11.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.4"}, "jest": {"testEnvironment": "node", "testTimeout": 30000, "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "testMatch": ["**/tests/**/*.test.js"], "verbose": true}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "本地项目"}, "bugs": {"url": "项目issue地址"}, "homepage": "项目主页"}