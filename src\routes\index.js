/**
 * API路由主入口
 * 统一管理所有API路由
 */

const express = require('express');
const router = express.Router();

// 导入各个路由模块
const unlockRoutes = require('./unlockRoutes');
const sourceRoutes = require('./sourceRoutes');

// 导入配置
const config = require('../config/config');

// 导入中间件
// const { requestLogger } = require('../middleware/logger');

// API版本信息
router.get('/', (req, res) => {
    res.json({
        name: '音乐解锁服务API',
        version: '1.0.0',
        description: '基于UnblockNeteaseMusic的音乐解锁服务后端API',
        endpoints: {
            解锁: '/api/unlock',
            音源: '/api/sources'
        },
        documentation: '/api/docs',
        health: '/health',
        timestamp: new Date().toISOString()
    });
});

// API文档路由
router.get('/docs', (req, res) => {
    res.json({
        title: '音乐解锁服务API文档',
        version: '1.0.0',
        baseUrl: `${req.protocol}://${req.get('host')}/api`,
        endpoints: [
            {
                group: '万能解锁服务',
                routes: [
                    {
                        method: 'GET',
                        path: '/unlock',
                        description: '万能解锁API - 统一处理所有解锁相关功能',
                        parameters: {
                            query: {
                                songIds: '歌曲ID，支持单个或逗号分隔的多个ID',
                                mode: '操作模式 (unlock|status|test|sources|detail)',
                                format: '返回格式 (full|minimal|head)',
                                detailed: '是否返回详细信息 (true|false)',
                                sources: '指定音源列表，逗号分隔',
                                minBitrate: '最低音质要求',
                                testSongId: '测试模式下使用的歌曲ID'
                            }
                        }
                    }
                ]
            },
            {
                group: '音源管理',
                routes: [
                    {
                        method: 'GET',
                        path: '/sources',
                        description: '获取所有可用音源列表',
                        parameters: {
                            query: {}
                        }
                    },
                    {
                        method: 'GET',
                        path: '/sources/:sourceId',
                        description: '获取单个音源详细信息',
                        parameters: {
                            path: { sourceId: '音源ID' }
                        }
                    },
                    {
                        method: 'GET',
                        path: '/sources/stats',
                        description: '获取音源统计信息',
                        parameters: {
                            query: {}
                        }
                    },
                    {
                        method: 'GET',
                        path: '/sources/config',
                        description: '获取音源配置信息',
                        parameters: {
                            query: {}
                        }
                    }
                ]
            }
        ],
        examples: {
            unlockSong: {
                url: '/api/unlock',
                method: 'GET',
                query: 'songIds=418602084&mode=unlock&sources=qq,kugou'
            },
            batchUnlock: {
                url: '/api/unlock',
                method: 'GET',
                query: 'songIds=418602084,186016&mode=unlock&sources=qq,kugou&minBitrate=320000'
            },
            checkStatus: {
                url: '/api/unlock',
                method: 'GET',
                query: 'songIds=418602084&mode=status&sources=qq,kugou'
            },
            testSources: {
                url: '/api/unlock',
                method: 'GET',
                query: 'mode=test&sources=qq,kugou&testSongId=418602084'
            },
            getSources: {
                url: '/api/sources',
                method: 'GET',
                query: ''
            }
        }
    });
});

// 配置API端点 (P2硬编码优化)
router.get('/config', (req, res) => {
    res.json({
        testing: {
            defaultSongId: config.testing.defaultSongId,
            defaultSongIds: config.testing.defaultSongIds,
            testKeywords: config.testing.testKeywords,
            testSources: config.testing.testSources,
            testArtists: config.testing.testArtists,
            testAlbums: config.testing.testAlbums
        },
        ui: {
            theme: config.ui.theme,
            layout: config.ui.layout
        },
        timestamp: new Date().toISOString()
    });
});

// 挂载各个路由模块
router.use('/unlock', unlockRoutes);
router.use('/sources', sourceRoutes);

module.exports = router;
