# Page snapshot

```yaml
- banner:
  - heading "🎵 音乐解锁服务" [level=1]
  - paragraph: 基于UnblockNeteaseMusic的音乐解锁服务后端API测试页面
  - text: 🟢 服务正常 v1.0.0
- navigation:
  - button "歌曲信息"
  - button "搜索功能"
  - button "解锁服务"
  - button "音源管理"
  - button "API文档"
- heading "🔍 音乐搜索" [level=2]
- text: "搜索类型:"
- combobox "搜索类型:":
  - option "关键词搜索" [selected]
  - option "ID搜索"
  - option "批量搜索"
- text: "搜索内容:"
- textbox "搜索内容:": 周杰伦
- text: "输入歌手名、歌曲名或专辑名进行搜索 分页设置:"
- spinbutton: "1"
- spinbutton: "20"
- text: "指定音源 (可选):"
- textbox "指定音源 (可选):"
- button "开始搜索"
- button "获取搜索建议"
- button "热门搜索"
- contentinfo:
  - paragraph:
    - text: © 2024 音乐解锁服务 | 基于
    - link "UnblockNeteaseMusic":
      - /url: https://github.com/UnblockNeteaseMusic/server
  - paragraph: 仅供学习和研究使用，请支持正版音乐
```