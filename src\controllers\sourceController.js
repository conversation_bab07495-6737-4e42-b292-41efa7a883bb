/**
 * 音源管理控制器
 * 处理音源相关的API请求
 */

const { getAvailableSources } = require('../services/unlockService');
const { success, badRequest } = require('../utils/response');
const { asyncHandler } = require('../middleware/errorHandler');
const { logBusiness } = require('../middleware/logger');
const { MUSIC_SOURCES, SOURCE_DISPLAY_NAMES } = require('../utils/constants');
const config = require('../config/config');

/**
 * 获取所有可用音源列表
 * GET /api/sources
 */
const getSources = asyncHandler(async (req, res) => {
    logBusiness('获取音源列表');

    const sources = await getAvailableSources();

    // 转换为中文字段名
    const chineseSources = sources.map(source => ({
        音源ID: source.id,
        音源名称: source.name,
        已启用: source.enabled,
        优先级: source.priority,
        ...(source.状态 && { 状态: source.状态 }),
        ...(source.最后检查时间 && { 最后检查时间: source.最后检查时间 }),
        ...(source.错误信息 && { 错误信息: source.错误信息 })
    }));

    const response = {
        音源列表: chineseSources,
        总数: chineseSources.length,
        已启用: chineseSources.filter(s => s.已启用).length
    };

    success(res, response, '音源列表获取成功');
});

/**
 * 获取单个音源详细信息
 * GET /api/sources/:sourceId
 */
const getSourceDetail = asyncHandler(async (req, res) => {
    const { sourceId } = req.params;

    logBusiness('获取音源详情', { sourceId });

    // 验证音源ID
    if (!Object.values(MUSIC_SOURCES).includes(sourceId)) {
        return badRequest(res, `不支持的音源: ${sourceId}`);
    }

    const sourceDetail = {
        音源ID: sourceId,
        音源名称: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
        已启用: config.music.sources.includes(sourceId),
        优先级: config.music.sources.indexOf(sourceId) + 1 || 999,
        功能特性: getSourceFeatures(sourceId)
    };

    success(res, sourceDetail, '音源详情获取成功');
});


/**
 * 获取音源统计信息
 * GET /api/sources/stats
 */
const getSourceStats = asyncHandler(async (req, res) => {
    logBusiness('获取音源统计');

    const sources = await getAvailableSources();

    // 这里可以添加更多统计信息，比如从数据库获取使用频率等
    const stats = {
        音源总数: sources.length,
        已启用音源: sources.filter(s => s.enabled).length,
        已禁用音源: sources.filter(s => !s.enabled).length,
        音源详情: sources.map(source => ({
            音源ID: source.id,
            音源名称: source.name,
            已启用: source.enabled,
            优先级: source.priority
        })),
        最后更新: new Date().toISOString()
    };

    success(res, stats, '音源统计信息获取成功');
});

/**
 * 获取音源配置信息
 * GET /api/sources/config
 */
const getSourceConfig = asyncHandler(async (req, res) => {
    logBusiness('获取音源配置');

    const sourceConfig = {
        已启用音源: config.music.sources,
        音源顺序: config.music.sources.map((sourceId, index) => ({
            音源ID: sourceId,
            音源名称: SOURCE_DISPLAY_NAMES[sourceId] || sourceId,
            优先级: index + 1
        })),
        设置: {
            遵循音源顺序: config.music.followSourceOrder,
            启用无损: config.music.enableFlac,
            音质处理: 'UnblockNeteaseMusic自动管理',
            启用本地VIP: config.music.enableLocalVip
        }
    };

    success(res, sourceConfig, '音源配置获取成功');
});

/**
 * 获取音源特性信息
 * @param {string} sourceId - 音源ID
 * @returns {Object} 音源特性
 */
function getSourceFeatures(sourceId) {
    const features = {
        [MUSIC_SOURCES.QQ]: {
            支持格式: ['mp3', 'm4a', 'flac'],
            最大音质: 999000,
            支持歌词: true,
            支持封面: true,
            需要Cookie: true
        },
        [MUSIC_SOURCES.KUGOU]: {
            支持格式: ['mp3', 'flac'],
            最大音质: 999000,
            支持歌词: true,
            支持封面: true,
            需要Cookie: false
        },
        [MUSIC_SOURCES.KUWO]: {
            支持格式: ['mp3'],
            最大音质: 320000,
            支持歌词: true,
            支持封面: true,
            需要Cookie: false
        },
        [MUSIC_SOURCES.MIGU]: {
            支持格式: ['mp3', 'flac'],
            最大音质: 999000,
            支持歌词: true,
            支持封面: true,
            需要Cookie: false
        },
        [MUSIC_SOURCES.JOOX]: {
            支持格式: ['mp3', 'm4a'],
            最大音质: 320000,
            支持歌词: false,
            支持封面: true,
            需要Cookie: true
        },
        [MUSIC_SOURCES.YOUTUBE]: {
            支持格式: ['mp3', 'm4a'],
            最大音质: 192000,
            支持歌词: false,
            支持封面: true,
            需要Cookie: false
        }
    };

    return features[sourceId] || {
        支持格式: ['mp3'],
        最大音质: 128000,
        支持歌词: false,
        支持封面: false,
        需要Cookie: false
    };
}

module.exports = {
    getSources,
    getSourceDetail,
    getSourceStats,
    getSourceConfig
};
