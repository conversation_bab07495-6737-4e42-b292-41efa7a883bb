{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\解锁3\\playwright.config.js", "rootDir": "C:/Users/<USER>/Desktop/解锁3/tests", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/解锁3/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/解锁3/tests", "testIgnore": [], "testMatch": ["**/e2e-*.test.js"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/解锁3/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Desktop/解锁3/tests", "testIgnore": [], "testMatch": ["**/e2e-*.test.js"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/解锁3/test-results", "repeatEach": 1, "retries": 2, "metadata": {"actualWorkers": 1}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Desktop/解锁3/tests", "testIgnore": [], "testMatch": ["**/e2e-*.test.js"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 1, "webServer": {"command": "npm start", "port": 3000, "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "e2e-html.test.js", "file": "e2e-html.test.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "音乐解锁服务HTML测试页面", "file": "e2e-html.test.js", "line": 12, "column": 6, "specs": [{"title": "页面基本元素加载", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 11258, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:30:51", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}, "snippet": "\u001b[0m \u001b[90m 28 |\u001b[39m         \u001b[90m// 检查主要功能模块是否存在\u001b[39m\n \u001b[90m 29 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 32 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#sources-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 28 |\u001b[39m         \u001b[90m// 检查主要功能模块是否存在\u001b[39m\n \u001b[90m 29 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 32 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#sources-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:30:51\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T21:08:55.124Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}}, {"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 11137, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:30:51", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}, "snippet": "\u001b[0m \u001b[90m 28 |\u001b[39m         \u001b[90m// 检查主要功能模块是否存在\u001b[39m\n \u001b[90m 29 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 32 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#sources-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 28 |\u001b[39m         \u001b[90m// 检查主要功能模块是否存在\u001b[39m\n \u001b[90m 29 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 32 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#sources-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:30:51\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-30T21:09:07.260Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}}, {"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 11153, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:30:51", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}, "snippet": "\u001b[0m \u001b[90m 28 |\u001b[39m         \u001b[90m// 检查主要功能模块是否存在\u001b[39m\n \u001b[90m 29 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 32 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#sources-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#search-tab')\nExpected: visible\nReceived: hidden\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('#search-tab')\u001b[22m\n\u001b[2m    14 × locator resolved to <div id=\"search-tab\" class=\"tab-content\">…</div>\u001b[22m\n\u001b[2m       - unexpected value \"hidden\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 28 |\u001b[39m         \u001b[90m// 检查主要功能模块是否存在\u001b[39m\n \u001b[90m 29 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 30 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                   \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 31 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 32 |\u001b[39m         \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'#sources-tab'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m 33 |\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:30:51\u001b[22m"}], "stdout": [], "stderr": [], "retry": 2, "startTime": "2025-07-30T21:09:19.210Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry2\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry2\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry2\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-页面基本元素加载-chromium-retry2\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 51, "line": 30}}], "status": "unexpected"}], "id": "614457eaf0330138a081-1bde4aac63ea8f5d3dec", "file": "e2e-html.test.js", "line": 24, "column": 5}, {"title": "搜索功能测试", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 11425, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    24 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    24 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:49:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m\n \u001b[90m 48 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#search-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 52 |\u001b[39m         \u001b[36mconst\u001b[39m searchResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    24 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m\n \u001b[90m 48 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#search-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 52 |\u001b[39m         \u001b[36mconst\u001b[39m searchResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:49:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T21:09:31.192Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}}, {"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 11493, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    25 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    25 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:49:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m\n \u001b[90m 48 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#search-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 52 |\u001b[39m         \u001b[36mconst\u001b[39m searchResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    25 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m\n \u001b[90m 48 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#search-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 52 |\u001b[39m         \u001b[36mconst\u001b[39m searchResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:49:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-30T21:09:43.386Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}}, {"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 11573, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    24 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    24 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:49:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}, "snippet": "\u001b[0m \u001b[90m 47 |\u001b[39m\n \u001b[90m 48 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#search-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 52 |\u001b[39m         \u001b[36mconst\u001b[39m searchResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}, "message": "TimeoutError: page.waitForSelector: Timeout 10000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#search-result') to be visible\u001b[22m\n\u001b[2m    24 × locator resolved to hidden <div id=\"search-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 47 |\u001b[39m\n \u001b[90m 48 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 49 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#search-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m10000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 50 |\u001b[39m\n \u001b[90m 51 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 52 |\u001b[39m         \u001b[36mconst\u001b[39m searchResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#search-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:49:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 2, "startTime": "2025-07-30T21:09:55.641Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry2\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry2\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry2\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-搜索功能测试-chromium-retry2\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 49}}], "status": "unexpected"}], "id": "614457eaf0330138a081-44b5a3faf75878b42e70", "file": "e2e-html.test.js", "line": 40, "column": 5}, {"title": "歌曲信息获取功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 16316, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:66:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m\n \u001b[90m 65 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#song-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m\n \u001b[90m 68 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 69 |\u001b[39m         \u001b[36mconst\u001b[39m songResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}, "message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 64 |\u001b[39m\n \u001b[90m 65 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#song-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m\n \u001b[90m 68 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 69 |\u001b[39m         \u001b[36mconst\u001b[39m songResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:66:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T21:10:07.951Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}}, {"workerIndex": 7, "parallelIndex": 0, "status": "failed", "duration": 16334, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:66:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m\n \u001b[90m 65 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#song-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m\n \u001b[90m 68 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 69 |\u001b[39m         \u001b[36mconst\u001b[39m songResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}, "message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 64 |\u001b[39m\n \u001b[90m 65 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#song-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m\n \u001b[90m 68 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 69 |\u001b[39m         \u001b[36mconst\u001b[39m songResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:66:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-30T21:10:25.040Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}}, {"workerIndex": 8, "parallelIndex": 0, "status": "failed", "duration": 16331, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:66:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}, "snippet": "\u001b[0m \u001b[90m 64 |\u001b[39m\n \u001b[90m 65 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#song-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m\n \u001b[90m 68 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 69 |\u001b[39m         \u001b[36mconst\u001b[39m songResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}, "message": "TimeoutError: page.waitForSelector: Timeout 15000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#song-result') to be visible\u001b[22m\n\u001b[2m    34 × locator resolved to hidden <div id=\"song-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 64 |\u001b[39m\n \u001b[90m 65 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 66 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#song-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 67 |\u001b[39m\n \u001b[90m 68 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 69 |\u001b[39m         \u001b[36mconst\u001b[39m songResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#song-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:66:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 2, "startTime": "2025-07-30T21:10:42.157Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry2\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry2\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry2\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲信息获取功能-chromium-retry2\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 66}}], "status": "unexpected"}], "id": "614457eaf0330138a081-71f386915019e1d0cda4", "file": "e2e-html.test.js", "line": 57, "column": 5}, {"title": "歌曲解锁功能", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "failed", "duration": 21658, "error": {"message": "TimeoutError: page.waitForSelector: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#unlock-result') to be visible\u001b[22m\n\u001b[2m    44 × locator resolved to hidden <div id=\"unlock-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "TimeoutError: page.waitForSelector: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#unlock-result') to be visible\u001b[22m\n\u001b[2m    44 × locator resolved to hidden <div id=\"unlock-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:82:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 82}, "snippet": "\u001b[0m \u001b[90m 80 |\u001b[39m\n \u001b[90m 81 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 82 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#unlock-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m20000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 83 |\u001b[39m\n \u001b[90m 84 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 85 |\u001b[39m         \u001b[36mconst\u001b[39m unlockResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 82}, "message": "TimeoutError: page.waitForSelector: Timeout 20000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#unlock-result') to be visible\u001b[22m\n\u001b[2m    44 × locator resolved to hidden <div id=\"unlock-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 80 |\u001b[39m\n \u001b[90m 81 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 82 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#unlock-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m20000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 83 |\u001b[39m\n \u001b[90m 84 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 85 |\u001b[39m         \u001b[36mconst\u001b[39m unlockResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:82:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-30T21:10:59.281Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲解锁功能-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲解锁功能-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲解锁功能-chromium\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲解锁功能-chromium\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 82}}, {"workerIndex": 10, "parallelIndex": 0, "status": "interrupted", "duration": 4463, "error": {"message": "Error: page.waitForSelector: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('#unlock-result') to be visible\u001b[22m\n\u001b[2m    11 × locator resolved to hidden <div id=\"unlock-result\" class=\"result-container\"></div>\u001b[22m\n", "stack": "Error: page.waitForSelector: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('#unlock-result') to be visible\u001b[22m\n\u001b[2m    11 × locator resolved to hidden <div id=\"unlock-result\" class=\"result-container\"></div>\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:82:20", "location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 82}, "snippet": "\u001b[0m \u001b[90m 80 |\u001b[39m\n \u001b[90m 81 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 82 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#unlock-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m20000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 83 |\u001b[39m\n \u001b[90m 84 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 85 |\u001b[39m         \u001b[36mconst\u001b[39m unlockResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 82}, "message": "Error: page.waitForSelector: Test ended.\nCall log:\n\u001b[2m  - waiting for locator('#unlock-result') to be visible\u001b[22m\n\u001b[2m    11 × locator resolved to hidden <div id=\"unlock-result\" class=\"result-container\"></div>\u001b[22m\n\n\n\u001b[0m \u001b[90m 80 |\u001b[39m\n \u001b[90m 81 |\u001b[39m         \u001b[90m// 等待结果显示\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 82 |\u001b[39m         \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForSelector(\u001b[32m'#unlock-result'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m20000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 83 |\u001b[39m\n \u001b[90m 84 |\u001b[39m         \u001b[90m// 检查结果是否显示\u001b[39m\n \u001b[90m 85 |\u001b[39m         \u001b[36mconst\u001b[39m unlockResult \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#unlock-result'\u001b[39m)\u001b[33m.\u001b[39mtextContent()\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js:82:20\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-07-30T21:11:21.749Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲解锁功能-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲解锁功能-chromium-retry1\\video.webm"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Desktop\\解锁3\\test-results\\e2e-html-音乐解锁服务HTML测试页面-歌曲解锁功能-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\解锁3\\tests\\e2e-html.test.js", "column": 20, "line": 82}}], "status": "unexpected"}], "id": "614457eaf0330138a081-90627afd2824998d2e6d", "file": "e2e-html.test.js", "line": 73, "column": 5}, {"title": "音源管理功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-30c97fca132e018f11e1", "file": "e2e-html.test.js", "line": 89, "column": 5}, {"title": "系统状态检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-a5722e0da821c30dd3e1", "file": "e2e-html.test.js", "line": 104, "column": 5}, {"title": "错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-b2ceb8d7ba1200efdf1b", "file": "e2e-html.test.js", "line": 114, "column": 5}, {"title": "响应时间性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-fec5bfd9a7de56fdc1e0", "file": "e2e-html.test.js", "line": 130, "column": 5}, {"title": "界面交互测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-2cd0a818e07382b657da", "file": "e2e-html.test.js", "line": 151, "column": 5}, {"title": "页面基本元素加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-b503e0bb084476b321b5", "file": "e2e-html.test.js", "line": 24, "column": 5}, {"title": "搜索功能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-3985e429b5d47da0c14a", "file": "e2e-html.test.js", "line": 40, "column": 5}, {"title": "歌曲信息获取功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-5bd3882457972d21aa64", "file": "e2e-html.test.js", "line": 57, "column": 5}, {"title": "歌曲解锁功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-e2a77db4379dca25f53a", "file": "e2e-html.test.js", "line": 73, "column": 5}, {"title": "音源管理功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-425e513403f8c5b76abc", "file": "e2e-html.test.js", "line": 89, "column": 5}, {"title": "系统状态检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-3de2d66e2c165f508713", "file": "e2e-html.test.js", "line": 104, "column": 5}, {"title": "错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-b6dd55b431fe11022c92", "file": "e2e-html.test.js", "line": 114, "column": 5}, {"title": "响应时间性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-bd7e2ad9289cc2da8c89", "file": "e2e-html.test.js", "line": 130, "column": 5}, {"title": "界面交互测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-b2b03899e56777ac3af9", "file": "e2e-html.test.js", "line": 151, "column": 5}, {"title": "页面基本元素加载", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-cf4ece706b48d8d635a7", "file": "e2e-html.test.js", "line": 24, "column": 5}, {"title": "搜索功能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-39f72a84619aa58b5b04", "file": "e2e-html.test.js", "line": 40, "column": 5}, {"title": "歌曲信息获取功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-696c8f9450f8169ab5c7", "file": "e2e-html.test.js", "line": 57, "column": 5}, {"title": "歌曲解锁功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-af9561c1a421f098c48e", "file": "e2e-html.test.js", "line": 73, "column": 5}, {"title": "音源管理功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-cc60fb617403b75f3461", "file": "e2e-html.test.js", "line": 89, "column": 5}, {"title": "系统状态检查", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-bb8584e500c338612b73", "file": "e2e-html.test.js", "line": 104, "column": 5}, {"title": "错误处理测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-13426873153139bf0a64", "file": "e2e-html.test.js", "line": 114, "column": 5}, {"title": "响应时间性能测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-438ae672c0a7ff6a083b", "file": "e2e-html.test.js", "line": 130, "column": 5}, {"title": "界面交互测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-7b81b0fb65ae7126385d", "file": "e2e-html.test.js", "line": 151, "column": 5}]}, {"title": "API响应格式测试", "file": "e2e-html.test.js", "line": 170, "column": 6, "specs": [{"title": "JSON响应格式验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-0b1cac8ad67b7930f896", "file": "e2e-html.test.js", "line": 171, "column": 5}, {"title": "JSON响应格式验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-8a3510060629c7633d80", "file": "e2e-html.test.js", "line": 171, "column": 5}, {"title": "JSON响应格式验证", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "614457eaf0330138a081-1b9ec7c343a7d5e7627d", "file": "e2e-html.test.js", "line": 171, "column": 5}]}]}, {"title": "e2e-simple.test.js", "file": "e2e-simple.test.js", "column": 0, "line": 0, "specs": [], "suites": [{"title": "音乐解锁服务基础功能测试", "file": "e2e-simple.test.js", "line": 11, "column": 6, "specs": [{"title": "页面基本加载测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-7b1a3f7a7949669b7d59", "file": "e2e-simple.test.js", "line": 20, "column": 5}, {"title": "标签页切换功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-2d6f0deb01c64941bfad", "file": "e2e-simple.test.js", "line": 37, "column": 5}, {"title": "歌曲信息输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-a400036088ac4252d9fb", "file": "e2e-simple.test.js", "line": 55, "column": 5}, {"title": "搜索功能输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-9e494daeb88ac6b66b6a", "file": "e2e-simple.test.js", "line": 71, "column": 5}, {"title": "解锁功能输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-a142cfb1fc83fd4738fb", "file": "e2e-simple.test.js", "line": 87, "column": 5}, {"title": "音源管理按钮测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-902a495d76d91eb3a397", "file": "e2e-simple.test.js", "line": 103, "column": 5}, {"title": "服务状态指示器测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-ed7d6b8f6b5758bcfd69", "file": "e2e-simple.test.js", "line": 113, "column": 5}, {"title": "响应式布局测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-8da62d9b9492336b6888", "file": "e2e-simple.test.js", "line": 121, "column": 5}, {"title": "页面性能基础测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-7215f3929a1fe459a427", "file": "e2e-simple.test.js", "line": 133, "column": 5}, {"title": "页面基本加载测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-c6c315b09840a9c2c432", "file": "e2e-simple.test.js", "line": 20, "column": 5}, {"title": "标签页切换功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-c78551276011ed46774d", "file": "e2e-simple.test.js", "line": 37, "column": 5}, {"title": "歌曲信息输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-ae414092ac25b9764e23", "file": "e2e-simple.test.js", "line": 55, "column": 5}, {"title": "搜索功能输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-a0f049e0186350ea5340", "file": "e2e-simple.test.js", "line": 71, "column": 5}, {"title": "解锁功能输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-61193c04be54ce195623", "file": "e2e-simple.test.js", "line": 87, "column": 5}, {"title": "音源管理按钮测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-b6ad8cb790b861f8aaa4", "file": "e2e-simple.test.js", "line": 103, "column": 5}, {"title": "服务状态指示器测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-8d72005e868de8f6697a", "file": "e2e-simple.test.js", "line": 113, "column": 5}, {"title": "响应式布局测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-dd4b74b01fcfb71f3b63", "file": "e2e-simple.test.js", "line": 121, "column": 5}, {"title": "页面性能基础测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-a85e14e79a63a835080b", "file": "e2e-simple.test.js", "line": 133, "column": 5}, {"title": "页面基本加载测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-055dddc273b53a506437", "file": "e2e-simple.test.js", "line": 20, "column": 5}, {"title": "标签页切换功能", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-7dfd115331b943ccad61", "file": "e2e-simple.test.js", "line": 37, "column": 5}, {"title": "歌曲信息输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-81a2507493e7e93a3ab4", "file": "e2e-simple.test.js", "line": 55, "column": 5}, {"title": "搜索功能输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-dd845a3377b370d45cc6", "file": "e2e-simple.test.js", "line": 71, "column": 5}, {"title": "解锁功能输入框测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-aeff503f2a75e99b4450", "file": "e2e-simple.test.js", "line": 87, "column": 5}, {"title": "音源管理按钮测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-b1f399afd14c166ad772", "file": "e2e-simple.test.js", "line": 103, "column": 5}, {"title": "服务状态指示器测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-009202e0251634a650b7", "file": "e2e-simple.test.js", "line": 113, "column": 5}, {"title": "响应式布局测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-6c66bbf496ae411cb798", "file": "e2e-simple.test.js", "line": 121, "column": 5}, {"title": "页面性能基础测试", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [], "status": "skipped"}], "id": "30e60cd2f68961197d2c-54319280dbf1b9765437", "file": "e2e-simple.test.js", "line": 133, "column": 5}]}]}], "errors": [], "stats": {"startTime": "2025-07-30T21:08:54.510Z", "duration": 152762.343, "expected": 0, "skipped": 53, "unexpected": 4, "flaky": 0}}