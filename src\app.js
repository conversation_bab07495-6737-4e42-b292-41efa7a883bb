/**
 * 音乐解锁服务主应用入口
 * 基于Express.js和UnblockNeteaseMusic的音乐解锁服务后端
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// 导入配置和工具
const config = require('./config/config');
const { logger, requestLogger } = require('./middleware/logger');
const { errorHandler, notFoundHandler, setupProcessErrorHandlers } = require('./middleware/errorHandler');
const { success } = require('./utils/response');

// 设置进程级错误处理
setupProcessErrorHandlers();

// 创建Express应用
const app = express();

// 信任代理（用于获取真实IP）
app.set('trust proxy', 1);

// 安全中间件
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ['\'self\''],
            styleSrc: ['\'self\'', '\'unsafe-inline\''], // 保留CSS内联样式支持
            scriptSrc: ['\'self\''], // 移除unsafe-inline，提高安全性
            imgSrc: ['\'self\'', 'data:', 'https:'],
            connectSrc: ['\'self\''], // 允许AJAX请求
            fontSrc: ['\'self\''], // 允许字体加载
        },
    },
}));

// CORS配置
app.use(cors({
    origin: config.security.corsOrigin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 请求体解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务已移除 - 项目现为纯后端API服务

// 请求日志中间件
app.use(requestLogger);

// API限流中间件
const limiter = rateLimit({
    windowMs: config.security.rateLimitWindowMs,
    max: config.security.rateLimitMaxRequests,
    message: {
        code: 429,
        message: '请求过于频繁，请稍后再试',
        timestamp: new Date().toISOString()
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use('/api', limiter);

// 根路径 - 合并健康检查和API文档功能
app.get('/', (req, res) => {
    const packageInfo = require('../package.json');
    success(res, {
        // 健康检查信息
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: packageInfo.version,
        environment: config.server.env,
        uptime: process.uptime(),

        // API文档信息
        name: '音乐解锁服务API',
        description: '基于UnblockNeteaseMusic的音乐解锁服务后端API',
        baseUrl: `${req.protocol}://${req.get('host')}`,

        // API端点列表
        endpoints: {
            '根路径(健康检查+文档)': '/',
            'API信息': '/api/',
            'API详细文档': '/api/docs',
            '万能解锁服务': '/api/unlock',
            '音源列表': '/api/sources',
            '音源统计': '/api/sources/stats',
            '音源配置': '/api/sources/config',
            '单个音源详情': '/api/sources/:sourceId'
        },

        // 使用说明
        usage: {
            '健康检查': '访问此根路径即可获取服务状态',
            'API测试': '使用独立的HTML测试工具进行API测试',
            '详细文档': '访问 /api/docs 获取完整API文档'
        }
    }, '音乐解锁服务运行正常');
});

// API路由
app.use('/api', require('./routes'));

// 404错误处理
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// 启动服务器
const server = app.listen(config.server.port, config.server.host, () => {
    logger.info('🎵 音乐解锁服务启动成功', {
        host: config.server.host,
        port: config.server.port,
        environment: config.server.env,
        pid: process.pid
    });
  
    logger.info(`🔍 服务状态和API文档: http://${config.server.host}:${config.server.port}/`);
    logger.info(`🎯 API基础地址: http://${config.server.host}:${config.server.port}/api`);
    logger.info('📝 使用独立的HTML测试工具进行API测试');
});

// 优雅关闭处理
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

function gracefulShutdown(signal) {
    logger.info(`收到${signal}信号，开始优雅关闭服务器...`);
  
    server.close((err) => {
        if (err) {
            logger.error('服务器关闭时发生错误:', err);
            process.exit(1);
        }
    
        logger.info('服务器已优雅关闭');
        process.exit(0);
    });
  
    // 强制关闭超时
    setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
    }, 10000);
}

module.exports = app;
