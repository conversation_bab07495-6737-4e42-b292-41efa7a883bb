#!/bin/bash

# 🚀 音乐解锁服务 - 运行部署脚本
# 版本: v1.0.0
# 作者: 开发团队
# 描述: 服务启动、健康检查、监控配置的完整运行脚本

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="音乐解锁服务"
DEFAULT_PORT=3000
HEALTH_CHECK_URL="http://localhost:$DEFAULT_PORT/health"
PID_FILE="server.pid"
LOG_FILE="logs/server.log"
MAX_STARTUP_TIME=30
HEALTH_CHECK_INTERVAL=5

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# 检查端口是否被占用
check_port() {
    local port=${1:-$DEFAULT_PORT}
    if command -v lsof &> /dev/null; then
        if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null; then
            return 0  # 端口被占用
        fi
    elif command -v netstat &> /dev/null; then
        if netstat -ln | grep ":$port " >/dev/null; then
            return 0  # 端口被占用
        fi
    elif command -v ss &> /dev/null; then
        if ss -ln | grep ":$port " >/dev/null; then
            return 0  # 端口被占用
        fi
    fi
    return 1  # 端口未被占用
}

# 获取端口占用进程
get_port_process() {
    local port=${1:-$DEFAULT_PORT}
    if command -v lsof &> /dev/null; then
        lsof -Pi :$port -sTCP:LISTEN
    elif command -v netstat &> /dev/null; then
        netstat -tlnp | grep ":$port "
    fi
}

# 预启动检查
pre_start_check() {
    log_header "🔍 预启动检查"
    
    # 检查必要文件
    if [ ! -f "package.json" ]; then
        log_error "package.json文件不存在"
        exit 1
    fi
    
    if [ ! -f "src/app.js" ]; then
        log_error "应用入口文件不存在"
        exit 1
    fi
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        log_warning "依赖未安装，正在安装..."
        npm install
    fi
    
    # 检查环境配置
    if [ ! -f ".env" ]; then
        log_warning ".env文件不存在，使用默认配置"
        cp .env.example .env 2>/dev/null || true
    fi
    
    # 读取端口配置
    if [ -f ".env" ]; then
        PORT=$(grep "^PORT=" .env | cut -d'=' -f2 | tr -d ' ')
        if [ -n "$PORT" ]; then
            DEFAULT_PORT=$PORT
            HEALTH_CHECK_URL="http://localhost:$DEFAULT_PORT/health"
        fi
    fi
    
    # 检查端口占用
    if check_port $DEFAULT_PORT; then
        log_warning "端口 $DEFAULT_PORT 已被占用"
        log_info "占用进程信息:"
        get_port_process $DEFAULT_PORT
        
        read -p "是否要停止占用进程并继续? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            stop_service
        else
            log_error "无法启动服务，端口被占用"
            exit 1
        fi
    fi
    
    # 创建必要目录
    mkdir -p logs
    mkdir -p public
    
    log_success "预启动检查完成"
}

# 启动服务
start_service() {
    log_header "🚀 启动服务"
    
    # 检查是否已经运行
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            log_warning "服务已在运行 (PID: $PID)"
            return 0
        else
            log_info "清理过期的PID文件"
            rm -f $PID_FILE
        fi
    fi
    
    log_info "正在启动 $PROJECT_NAME..."
    log_info "端口: $DEFAULT_PORT"
    log_info "日志文件: $LOG_FILE"
    
    # 启动服务
    if [ "$1" = "dev" ]; then
        log_info "开发模式启动..."
        NODE_ENV=development npm run dev &
    elif [ "$1" = "debug" ]; then
        log_info "调试模式启动..."
        DEBUG=* npm start &
    else
        log_info "生产模式启动..."
        NODE_ENV=production npm start &
    fi
    
    # 保存PID
    echo $! > $PID_FILE
    PID=$(cat $PID_FILE)
    
    log_info "服务已启动 (PID: $PID)"
    log_success "服务启动命令执行完成"
}

# 健康检查
health_check() {
    log_header "🏥 健康检查"
    
    local max_attempts=$((MAX_STARTUP_TIME / HEALTH_CHECK_INTERVAL))
    local attempt=1
    
    log_info "等待服务启动..."
    log_info "健康检查URL: $HEALTH_CHECK_URL"
    
    while [ $attempt -le $max_attempts ]; do
        log_info "健康检查 ($attempt/$max_attempts)..."
        
        if command -v curl &> /dev/null; then
            if curl -s -f "$HEALTH_CHECK_URL" > /dev/null 2>&1; then
                log_success "服务健康检查通过"
                
                # 获取服务信息
                SERVICE_INFO=$(curl -s "$HEALTH_CHECK_URL" 2>/dev/null || echo '{"status":"unknown"}')
                log_info "服务状态: $SERVICE_INFO"
                return 0
            fi
        elif command -v wget &> /dev/null; then
            if wget -q --spider "$HEALTH_CHECK_URL" 2>/dev/null; then
                log_success "服务健康检查通过"
                return 0
            fi
        else
            # 使用Node.js进行检查
            if node -e "
                const http = require('http');
                const url = '$HEALTH_CHECK_URL';
                http.get(url, (res) => {
                    if (res.statusCode === 200) {
                        console.log('Health check passed');
                        process.exit(0);
                    } else {
                        process.exit(1);
                    }
                }).on('error', () => process.exit(1));
                setTimeout(() => process.exit(1), 5000);
            " 2>/dev/null; then
                log_success "服务健康检查通过"
                return 0
            fi
        fi
        
        sleep $HEALTH_CHECK_INTERVAL
        attempt=$((attempt + 1))
    done
    
    log_error "健康检查失败，服务可能启动异常"
    return 1
}

# 停止服务
stop_service() {
    log_header "🛑 停止服务"
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            log_info "正在停止服务 (PID: $PID)..."
            kill $PID
            
            # 等待进程结束
            local count=0
            while ps -p $PID > /dev/null 2>&1 && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 强制结束
            if ps -p $PID > /dev/null 2>&1; then
                log_warning "强制结束进程..."
                kill -9 $PID
            fi
            
            log_success "服务已停止"
        else
            log_warning "服务未运行"
        fi
        rm -f $PID_FILE
    else
        log_warning "PID文件不存在，尝试通过端口停止..."
        
        if check_port $DEFAULT_PORT; then
            if command -v lsof &> /dev/null; then
                PID=$(lsof -Pi :$DEFAULT_PORT -sTCP:LISTEN -t)
                if [ -n "$PID" ]; then
                    log_info "通过端口找到进程 (PID: $PID)，正在停止..."
                    kill $PID
                    log_success "服务已停止"
                fi
            fi
        else
            log_info "端口未被占用，服务可能已停止"
        fi
    fi
}

# 重启服务
restart_service() {
    log_header "🔄 重启服务"
    
    stop_service
    sleep 2
    start_service $1
    health_check
}

# 查看服务状态
status_service() {
    log_header "📊 服务状态"
    
    if [ -f "$PID_FILE" ]; then
        PID=$(cat $PID_FILE)
        if ps -p $PID > /dev/null 2>&1; then
            log_success "服务正在运行 (PID: $PID)"
            
            # 显示进程信息
            log_info "进程信息:"
            ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem
            
            # 显示端口信息
            if check_port $DEFAULT_PORT; then
                log_success "端口 $DEFAULT_PORT 正在监听"
            else
                log_warning "端口 $DEFAULT_PORT 未在监听"
            fi
            
            # 健康检查
            if command -v curl &> /dev/null; then
                if curl -s -f "$HEALTH_CHECK_URL" > /dev/null 2>&1; then
                    log_success "健康检查通过"
                    SERVICE_INFO=$(curl -s "$HEALTH_CHECK_URL" 2>/dev/null)
                    echo "服务信息: $SERVICE_INFO"
                else
                    log_warning "健康检查失败"
                fi
            fi
            
        else
            log_error "PID文件存在但进程未运行"
            rm -f $PID_FILE
        fi
    else
        log_warning "服务未运行 (PID文件不存在)"
        
        if check_port $DEFAULT_PORT; then
            log_warning "但端口 $DEFAULT_PORT 被其他进程占用"
            get_port_process $DEFAULT_PORT
        fi
    fi
}

# 查看日志
view_logs() {
    log_header "📋 查看日志"
    
    if [ -f "$LOG_FILE" ]; then
        log_info "显示最近50行日志:"
        tail -50 "$LOG_FILE"
    else
        log_warning "日志文件不存在: $LOG_FILE"
    fi
    
    # 实时日志
    if [ "$1" = "follow" ]; then
        log_info "实时日志 (Ctrl+C退出):"
        tail -f "$LOG_FILE" 2>/dev/null || log_warning "无法跟踪日志文件"
    fi
}

# 性能监控
monitor_service() {
    log_header "📈 性能监控"
    
    if [ ! -f "$PID_FILE" ]; then
        log_error "服务未运行"
        return 1
    fi
    
    PID=$(cat $PID_FILE)
    if ! ps -p $PID > /dev/null 2>&1; then
        log_error "服务进程不存在"
        return 1
    fi
    
    log_info "监控服务性能 (Ctrl+C退出)..."
    
    while true; do
        clear
        echo -e "${CYAN}🎵 $PROJECT_NAME 性能监控${NC}"
        echo "================================"
        echo "时间: $(date)"
        echo "PID: $PID"
        echo ""
        
        # 进程信息
        echo -e "${YELLOW}进程信息:${NC}"
        ps -p $PID -o pid,ppid,cmd,etime,pcpu,pmem,vsz,rss
        echo ""
        
        # 端口信息
        echo -e "${YELLOW}网络连接:${NC}"
        if command -v netstat &> /dev/null; then
            netstat -tlnp 2>/dev/null | grep ":$DEFAULT_PORT " || echo "端口未监听"
        fi
        echo ""
        
        # 健康检查
        echo -e "${YELLOW}健康状态:${NC}"
        if curl -s -f "$HEALTH_CHECK_URL" > /dev/null 2>&1; then
            echo -e "${GREEN}✅ 健康${NC}"
        else
            echo -e "${RED}❌ 异常${NC}"
        fi
        
        sleep 5
    done
}

# 显示帮助信息
show_help() {
    echo -e "${CYAN}🎵 $PROJECT_NAME 运行脚本${NC}"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start [dev|debug]  启动服务 (可选开发/调试模式)"
    echo "  stop              停止服务"
    echo "  restart [dev]     重启服务"
    echo "  status            查看服务状态"
    echo "  logs [follow]     查看日志 (可选实时跟踪)"
    echo "  monitor           性能监控"
    echo "  health            健康检查"
    echo "  help              显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start          # 生产模式启动"
    echo "  $0 start dev      # 开发模式启动"
    echo "  $0 logs follow    # 实时查看日志"
    echo "  $0 restart        # 重启服务"
    echo ""
    echo "配置:"
    echo "  端口: $DEFAULT_PORT (可在.env中修改)"
    echo "  日志: $LOG_FILE"
    echo "  PID: $PID_FILE"
    echo ""
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            pre_start_check
            start_service $2
            health_check
            log_success "🎉 服务启动完成！"
            echo ""
            echo -e "${GREEN}📋 访问信息:${NC}"
            echo -e "  🌐 测试页面: http://localhost:$DEFAULT_PORT"
            echo -e "  📡 API文档: http://localhost:$DEFAULT_PORT/api/docs"
            echo -e "  🏥 健康检查: http://localhost:$DEFAULT_PORT/health"
            echo ""
            echo -e "${CYAN}💡 常用命令:${NC}"
            echo -e "  查看状态: $0 status"
            echo -e "  查看日志: $0 logs"
            echo -e "  停止服务: $0 stop"
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            restart_service $2
            ;;
        "status")
            status_service
            ;;
        "logs")
            view_logs $2
            ;;
        "monitor")
            monitor_service
            ;;
        "health")
            health_check
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
