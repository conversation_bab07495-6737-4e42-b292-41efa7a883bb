/**
 * 参数验证中间件模块
 * 基于Joi实现的请求参数验证系统
 */

const Jo<PERSON> = require('joi');
const { badRequest } = require('../utils/response');
const { API_LIMITS } = require('../utils/constants');

/**
 * 创建验证中间件
 * @param {Object} schema - Joi验证模式
 * @param {string} source - 验证数据源 ('body', 'query', 'params')
 * @returns {Function} Express中间件函数
 */
function createValidator(schema, source = 'body') {
    return (req, res, next) => {
        try {
            const data = req[source];

            // 检查数据是否存在
            if (!data && source !== 'query') {
                return badRequest(res, `缺少${source}参数`);
            }

            const { error, value } = schema.validate(data, {
                abortEarly: false, // 返回所有验证错误
                stripUnknown: true, // 移除未知字段
                allowUnknown: false, // 不允许未知字段
                convert: true // 自动类型转换
            });

            if (error) {
                const details = error.details.map(detail => ({
                    field: detail.path.join('.'),
                    message: detail.message,
                    value: detail.context?.value,
                    type: detail.type
                }));

                return badRequest(res, '请求参数验证失败', details);
            }

            // 将验证后的数据替换原始数据
            req[source] = value;
            next();
        } catch (validationError) {
            return badRequest(res, `参数验证过程出错: ${validationError.message}`);
        }
    };
}

// 通用验证模式
const commonSchemas = {
    // 歌曲ID验证
    songId: Joi.alternatives().try(
        Joi.number().integer().positive(),
        Joi.string().pattern(/^\d+$/).custom((value) => parseInt(value))
    ).required().messages({
        'any.required': '歌曲ID是必需的',
        'number.positive': '歌曲ID必须是正整数',
        'string.pattern.base': '歌曲ID格式不正确'
    }),

    // 搜索关键词验证
    keyword: Joi.string()
        .trim()
        .min(1)
        .max(API_LIMITS.MAX_KEYWORD_LENGTH)
        .required()
        .messages({
            'string.empty': '搜索关键词不能为空',
            'string.min': '搜索关键词至少包含1个字符',
            'string.max': `搜索关键词不能超过${API_LIMITS.MAX_KEYWORD_LENGTH}个字符`,
            'any.required': '搜索关键词是必需的'
        }),

    // 音源列表验证
    sources: Joi.array()
        .items(Joi.string().valid('qq', 'kugou', 'kuwo', 'migu', 'joox', 'youtube', 'ytdlp', 'bilibili'))
        .min(1)
        .unique()
        .messages({
            'array.min': '至少需要指定一个音源',
            'array.unique': '音源列表不能包含重复项',
            'any.only': '不支持的音源类型'
        }),

    // 分页参数验证
    pagination: {
        page: Joi.number().integer().min(1).default(1),
        pageSize: Joi.number().integer().min(1).max(100).default(20)
    }
};

// 具体接口的验证模式
const schemas = {
    // 获取歌曲信息 - 路径参数验证
    getSong: {
        params: Joi.object({
            id: commonSchemas.songId
        })
    },




    // 批量解锁歌曲 - 请求体验证
    unlockSongs: {
        body: Joi.object({
            // 歌曲ID列表
            songIds: Joi.array()
                .items(commonSchemas.songId)
                .min(1)
                .max(API_LIMITS.MAX_BATCH_SIZE)
                .unique()
                .required(),

            // 指定音源（可选）
            sources: commonSchemas.sources.optional(),

            // 最低音质要求 - 简化验证，接受任何非负整数
            minBitrate: Joi.number()
                .integer()
                .min(0)
                .default(0),

            // 是否返回详细信息
            detailed: Joi.boolean().default(false)
        })
    },

    // 获取音源列表 - 查询参数验证
    getSources: {
        query: Joi.object({
            // 是否包含状态信息
            includeStatus: Joi.boolean().default(false)
        })
    }
};

// 导出验证中间件
const validators = {
    // 获取歌曲信息验证
    validateGetSong: createValidator(schemas.getSong.params, 'params'),



    // 批量解锁验证
    validateUnlockSongs: createValidator(schemas.unlockSongs.body, 'body'),

    // 获取音源列表验证
    validateGetSources: createValidator(schemas.getSources.query, 'query')
};

module.exports = {
    createValidator,
    commonSchemas,
    schemas,
    validators
};
