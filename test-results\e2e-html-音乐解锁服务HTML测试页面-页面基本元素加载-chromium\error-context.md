# Page snapshot

```yaml
- banner:
  - heading "🎵 音乐解锁服务" [level=1]
  - paragraph: 基于UnblockNeteaseMusic的音乐解锁服务后端API测试页面
  - text: 🟢 服务正常 v1.0.0
- navigation:
  - button "歌曲信息"
  - button "搜索功能"
  - button "解锁服务"
  - button "音源管理"
  - button "API文档"
- heading "🎵 歌曲信息获取" [level=2]
- text: "歌曲ID:"
- textbox "歌曲ID:": "418602084"
- text: "网易云音乐歌曲ID，可从歌曲链接中获取 指定音源 (可选):"
- textbox "指定音源 (可选):"
- text: 多个音源用逗号分隔，留空使用默认音源
- button "获取歌曲信息"
- button "仅获取元数据"
- button "检查可用性"
- contentinfo:
  - paragraph:
    - text: © 2024 音乐解锁服务 | 基于
    - link "UnblockNeteaseMusic":
      - /url: https://github.com/UnblockNeteaseMusic/server
  - paragraph: 仅供学习和研究使用，请支持正版音乐
```